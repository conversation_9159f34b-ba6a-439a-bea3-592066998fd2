import datetime
import ipaddress
import json
import logging
import random
import socket
import sys
import time
import traceback
from typing import Optional

import requests

import constants
import db
import log
from models.area import get_country_by_id
from models.ip_order import get_payway_by_id, set_ip_order, chang_ip_order_status, get_payway, set_order_user_token_id
from models.proxy import (
    choose_static_private_ips,
    change_proxy_ips_online,
    add_user_token,
    get_static_private_ip_by_token_id,
    get_proxy_using_count,
    change_user_token_life_time,
    delete_token_by_id,
    get_proxy_obj_by_id,
    get_iproyal_location_by_country_id,
    save_iproyal_order,
    add_proxy_ips,
    get_supplier_by_name,
    get_iproyal_order_by_pi_id,
    get_proxy_num,
    get_ipaddr_by_pid, get_lief_1_list, re_new_ip, updata_token,
)
from models.user import get_user_by_uid
from src.base import ResponseBase, MakeProxyBase, ByArea
from src.exceptions import *
from utils.decorators import timer
from utils.functions import testProxy, randomString

logger = log.get_logger(__name__)



class BuyPrivateStaticIP(ResponseBase, MakeProxyBase, ByArea):
    SUPPLIERS = (
        "instant_proxy",
        "iproyal_data_center",
        "iproyal_residential",
    )

    BACKUP_PROXIES_NUM = 3  # type: int  # 备选代理个数， 用于第一个挑选的代理不能用的情况下 备选
    only_socks=False
    REDIS_LOCK_PROXIES_ID_KEY = "lock_proxies_id_set"
    LOCK_USER_KEY = "lock_creating_private_user_id_"

    _is_data_center = (
        None
    )  # type: Optional[bool]   # True: user DATA_CENTER_SUPPLIERS; Flase: user RESIDENTIAL_SUPPLIERS; None: RESIDENTIAL_SUPPLIERS + DATA_CENTER_SUPPLIERS

    def __init__(self, payway_id: int,is_auto_renew: bool = False):
        self.payway = self.getBuyPrivateStaticIPPayway(payway_id)
        self.is_auto_renew = is_auto_renew
        self.execute_user = None

    def getBuyPrivateStaticIPPayway(self, payway_id: int) -> Optional[dict]:
        payway = get_payway_by_id(payway_id)
        logger.info(f"Payway: {payway}")
        return (
            None
            if not payway
            or payway["payway"] not in ("buy_data_center_private_static_ip", "buy_residential_private_static_ip")
            else payway
        )

    def getRefundPrivateStaticIPPayway(self, is_data_center: bool) -> Optional[dict]:
        return get_payway(
            "refund_data_center_private_static_ip" if is_data_center else "refund_residential_private_static_ip"
        )

    def check_proxy(self, proxy_obj) -> bool:
        _proxy = f"{('http' if proxy_obj.get('only_http') else 'socks5') if not proxy_obj.get('only_socks') else 'socks5'}://"
        if "username" in proxy_obj and proxy_obj["username"]:
            _proxy += f"{proxy_obj['username']}:{proxy_obj.get('password',proxy_obj.get('passwd',''))}@"
        host = proxy_obj["domain"] or ipaddress.ip_address(proxy_obj["ip_addr"] or int(proxy_obj["ip_address"], 16))
        _proxy += f"{host}:{proxy_obj['forward_port']}"
        return testProxy(
            _proxy
        )

    def _pay(self, payway: dict):
        return set_ip_order(
            uid=self.uid,
            type=payway["type"],
            currency_type="",
            currency=0,
            receive_currency=0,
            is_inner=False,
            payway=payway["payway"],
            pay_order="",
            value=payway["pay_value"],
            extra_value=payway["extra_value"],
            status=1,
            checksum=True,
            valid=True,
        )

    def _refund(self, refund_payway: dict, user_token_id: int = 0, pay_order_id=None):
        if pay_order_id:
            chang_ip_order_status(pay_order_id, status=2)
        set_ip_order(
            uid=self.uid,
            type=refund_payway["type"],
            currency_type="",
            currency=0,
            receive_currency=0,
            is_inner=False,
            payway=refund_payway["payway"],
            pay_order="",
            value=self.payway["pay_value"],
            extra_value=self.payway["extra_value"],
            status=2,
            checksum=True,
            valid=True,
            ut_id=user_token_id,
        )

    @timer
    def _chooseProxy(self, proxies):

        tage_proxy = None  # 目标代理
        for proxy in proxies:
            if self.check_proxy(proxy):
                tage_proxy = proxy
                break
            else:
                change_proxy_ips_online(proxy["id"], online=0)  # set proxy not online
        if not tage_proxy:  # all proxies in find was die, return no proxy error
            return self.noProxyDo()
        pay_order_id = self._pay(self.payway)
        if not pay_order_id:  # pay error, return balance not
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH)
        token_username, token_password = randomString(8), randomString(16)
        url = random.choice(constants.PROXY_SERVERS_LIST)
        if self._is_data_center and self.payway["days"]>0:
            url = 'proxy2.proxy302.com'

        token = add_user_token(
            uid=self.uid,
            url=url,
            username=token_username,
            passwd=token_password,
            pi_id=tage_proxy["id"],
            network=self.network,
            life_time=self.payway["days"],
            is_static=True,
            is_data_center=tage_proxy["is_data_center"],
            ps_id=tage_proxy["ps_id"],
        )  # save in token
        if 0 >= token:  # save token error
            self._refund(
                refund_payway=self.getRefundPrivateStaticIPPayway(is_data_center=tage_proxy["is_data_center"]),
                user_token_id=token,
                pay_order_id=pay_order_id,
            )
            return self.returnError(constants.ERR_NEW_PROXY_FAILED)
        # 更新 order上的记录
        logger.info(f"orderid:{pay_order_id},token:{token}")
        updata_token(pay_order_id,token)

        iproyal_order = {}
        if self.payway["days"] >= 30:
            try:
                iproyal_order = get_iproyal_order_by_pi_id(tage_proxy["id"]) or {
                    "created_on": tage_proxy["created_on"],
                    "order_id": "",
                }

                # 获取用户信息
                u = get_user_by_uid(self.uid)

                # 设置时区和当前时间戳
                tz = datetime.timezone(datetime.timedelta(hours=8))  # UTC+8
                now_ts = int(time.time())

                # 获取ip

                try:
                    ip = tage_proxy.get("ip_addr", None)
                    ip = socket.inet_ntoa(int(ip).to_bytes(4, "big"))
                except Exception:
                    ip = ""

                # 计算一些时间差和格式化时间字符串
                created_on_dt = datetime.datetime.fromtimestamp(iproyal_order["created_on"], tz)
                created_on_str = created_on_dt.strftime("%Y-%m-%d")
                now_dt = datetime.datetime.fromtimestamp(now_ts, tz)
                now_str = now_dt.strftime("%Y-%m-%d")
                expiry_dt = now_dt + datetime.timedelta(days=self.payway["days"])
                expiry_str = expiry_dt.strftime("%Y-%m-%d")
                cost_days = (now_dt - created_on_dt).days

                # 构建消息字符串
                result = (
                    f"用户: {u['email']} 购买数据库中的IProyal代理 {self.payway['days']}天 IP: {ip}\n"
                    + f"代理生成日期: {now_str} 代理失效时间: {expiry_str} 在库时间: {cost_days}天\n"
                    + f"对应的IProyal订单号: #{iproyal_order['order_id']} 创建日期: {created_on_str}"
                )

                # 发送消息
                constants.feishu.sendText(result)

            except:
                traceback.print_exc()

        token_id = token
        self.insert_traffic(token_id,pay_order_id)

        return self.returnSucceed({"token_id": token_id})

    def insert_traffic(self,token_id,pay_order_id,order_id=0):
        if not order_id:
            order_id = 0
        db.mysql.execute("""
                insert into t_static_ip_renew (uid,token_id,created_on,is_auto_renew,created_by,pay_way_id) values
                (%s,%s,%s,%s,%s,%s);
                """, *(
         self.uid, token_id,  int(time.time()), self.is_auto_renew, -1, self.payway["id"]))
        db.mysql.execute("""
        insert into t_static_ip_renew (order_id,uid,token_id,t_ip_order_id,created_on,is_auto_renew,created_by,pay_way_id) values
        (%s,%s,%s,%s,%s,%s,%s,%s);
        """, *(order_id, self.uid, token_id, pay_order_id, int(time.time()), self.is_auto_renew, self.uid,self.payway["id"]))

    def buy(self):
        try:
            if not self.payway:
                return self.returnError(constants.ERR_NOT_PAYWAY)
            if not self.uid:
                return UserError()
            if db.redis.get(f"{self.LOCK_USER_KEY}{self.uid}"):
                return self.returnError(constants.ERR_NEW_PROXY_FREQUENTLY)
            db.redis.setex(f"{self.LOCK_USER_KEY}{self.uid}", int(datetime.timedelta(minutes=3).total_seconds()), 1)
            exclude_proxies_id = tuple(map(int, db.redis.smembers(self.REDIS_LOCK_PROXIES_ID_KEY)))  # get locking proxies
            in_list = tuple()
            is_one_days = False
            if self.payway.get("payway") in ("buy_residential_private_static_ip","buy_data_center_private_static_ip") and self.payway.get("days")==1:
                is_one_days = True
                how_many = get_lief_1_list(self.uid)
                if how_many:
                    # exclude_proxies_id = tuple([i for i in exclude_proxies_id if i not in how_many])
                    in_list = tuple(how_many)

            proxies = choose_static_private_ips(
                uid=self.uid,
                suppliers_name=self.SUPPLIERS,
                exclude_ids=exclude_proxies_id,
                num=self.BACKUP_PROXIES_NUM,
                country_id=self.country[0],
                state_id=self.state[0],
                city_id=self.city[0],
                in_list = in_list,
                is_one_days = is_one_days,
                only_socks = self.only_socks
            )
            logger.info(f"Proxies found for user {self.uid}: {proxies}")
            if not proxies:  # no proxy in find, return no proxy error
                result = self.noProxyDo()
                db.redis.delete(self.LOCK_USER_KEY + str(self.uid))
                return result
            proxies_id = [p.id for p in proxies]
            db.redis.sadd(self.REDIS_LOCK_PROXIES_ID_KEY, *proxies_id)  # lock proxies
            res = self._chooseProxy(proxies)
            db.redis.srem(self.REDIS_LOCK_PROXIES_ID_KEY, *proxies_id)  # unlock proxies
            db.redis.delete(f"{self.LOCK_USER_KEY}{self.uid}")
            return res if res is not None else self.returnSucceed()

        except Exception as e:
            logger.exception("An error occurred while executing the 'buy' method")
            return self.returnError(constants.ERR_NEW_PROXY_FREQUENTLY)

    def renew(self, token_id: int):
        """
        续费代理
        :param token_id:
        :return:
        """
        token = get_static_private_ip_by_token_id(uid=self.uid, token_id=token_id)
        if not token:
            return self.returnError(constants.ERR_NO_THIS_PROXY)
        if token["expired"]:
            return self.returnError(constants.ERR_PROXY_TOKEN_EXPIRED)
        if not all((token["health"], token["online"], token["status"])):
            return self.returnError(constants.ERR_PROXY_LOST_EFFICACY)
        pay_order_id = self._pay(self.payway)
        if not pay_order_id:
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH)
        re_new_ip(uid=self.uid, token_id=token_id, pay_way_id=self.payway["id"], is_auto_renew=self.is_auto_renew,
                  t_ip_order_id=pay_order_id,execute_user=self.execute_user)

        if change_user_token_life_time(token_id, self.payway["days"]):
            if token["life_time"] + self.payway["days"] >= 30:
                iproyal_order = get_iproyal_order_by_pi_id(token["pi_id"])
                if iproyal_order:
                    ip = get_ipaddr_by_pid(uid=self.uid, pi_id=token["pi_id"])

                    # 获取用户信息
                    u = get_user_by_uid(self.uid)

                    # 设置时区为UTC+8
                    tz = datetime.timezone(datetime.timedelta(hours=8))

                    # 将订单创建时间转换为字符串格式
                    created_on_dt = datetime.datetime.fromtimestamp(iproyal_order["created_on"], tz)
                    dt_str = created_on_dt.strftime("%Y-%m-%d")

                    # 计算代理失效时间并转换为字符串格式
                    expiry_dt = datetime.datetime.fromtimestamp(token["created_on"], tz) + datetime.timedelta(days=self.payway["days"]+int(token['life_time']))
                    exp_time = expiry_dt.strftime("%Y-%m-%d")

                    # 将Token创建时间转换为字符串格式
                    ct_dt = datetime.datetime.fromtimestamp(token["created_on"], tz)
                    ct_str = ct_dt.strftime("%Y-%m-%d")
                    total_buy_day = int(token['life_time']) + int(self.payway['days'])

                    result = (
                        f"用户:{u['email']} 续费IProyal代理 {self.payway['days']}天 总共购买了{total_buy_day}天  IP: {ip}\n"
                        + f" 代理生成日期: {ct_str}  代理失效时间{exp_time}\n"
                        + f"对应的IProyal订单号: #{iproyal_order['order_id']} 创建日期: {dt_str}"
                    )

                    constants.feishu.sendText(result)
            return self.returnSucceed()
        self._refund(
            refund_payway=self.getRefundPrivateStaticIPPayway(is_data_center=token["is_data_center"]),
            user_token_id=token,
            pay_order_id=pay_order_id,
        )
        return self.returnError(constants.ERR_NEW_PROXY_FAILED)

    def rebuy(self, token_id: int):
        """
        再次购买代理
        :param token_id:
        :return:
        """
        token = get_static_private_ip_by_token_id(uid=self.uid, token_id=token_id)
        if not token:
            return self.returnError(constants.ERR_NO_THIS_PROXY)
        if not token["expired"]:
            return self.returnError(constants.ERR_PROXY_NOT_IN_EXPIRED)
        if not all((token["health"], token["online"], token["status"])):
            return self.returnError(constants.ERR_PROXY_LOST_EFFICACY)
        proxy_obj = get_proxy_obj_by_id(token["pi_id"])
        if not proxy_obj:
            return self.returnError(constants.ERR_NO_THIS_PROXY)
        if not self.check_proxy(proxy_obj):
            return self.returnError(constants.ERR_PROXY_LOST_EFFICACY)
        if get_proxy_using_count(pi_id=token["pi_id"], not_in_uid=(self.uid,)) > 0:
            return self.returnError(constants.ERR_PROXY_LOST_EFFICACY)
        pay_order_id = self._pay(self.payway)
        if not pay_order_id:
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH)
        new_token = add_user_token(
            uid=self.uid,
            url=token["url"],
            username=token["username"],
            passwd=token["passwd"],
            pi_id=token["pi_id"],
            network=token["network"],
            life_time=self.payway["days"],
            is_static=True,
            is_data_center=token["data_center"],
            ps_id=token["ps_id"],
        )  # save in token
        if 0 >= new_token:  # save token error
            self._refund(
                refund_payway=self.getRefundPrivateStaticIPPayway(is_data_center=token["data_center"]),
                user_token_id=token,
                pay_order_id=pay_order_id,
            )
            return self.returnError(constants.ERR_NEW_PROXY_FAILED)
        delete_token_by_id(token["id"])
        if self.payway["days"] >= 30:  # 购买大于30天，报警提醒开启自动续费
            iproyal_order = get_iproyal_order_by_pi_id(token["pi_id"])
            if iproyal_order:
                u = get_user_by_uid(self.uid)
                tz = datetime.timezone(datetime.timedelta(hours=8))  # UTC+8
                dt_str = datetime.datetime.fromtimestamp(iproyal_order["created_on"], tz).strftime("%Y-%m-%d")
                ct_str = datetime.datetime.fromtimestamp(token["created_on"], tz).strftime("%Y-%m-%d")
                constants.feishu.sendText(
                    f"用户:{u['email']}  购买库存中的IProyal代理 {self.payway['days']}天 "
                    f"代理生成日期: {ct_str}  \n"
                    f"对应的IProyal订单号:#{iproyal_order['order_id']}   创建日期: {dt_str}"
                )
        return self.returnSucceed()

    def noProxyDo(self):
        return self.returnError(constants.ERR_NEW_PROXY_FAILED)


class BuyPrivateStaticIPDataCenterPrivateStaticIP(BuyPrivateStaticIP):
    _is_data_center = True
    SUPPLIERS = ("oracle_http_proxy","instant_proxy", "iproyal_data_center")

    def getBuyPrivateStaticIPPayway(self, payway_id: int) -> Optional[dict]:
        payway = get_payway_by_id(payway_id)
        return None if not payway or payway["payway"] != "buy_data_center_private_static_ip" else payway


class BuyPrivateStaticIPDataCenterPrivateStaticIPSocks5(BuyPrivateStaticIPDataCenterPrivateStaticIP):
    _is_data_center = True
    only_socks=True
    SUPPLIERS = ("oracle_socks_proxy",)


class ByResidentialPrivateStaticIP(BuyPrivateStaticIP):
    _is_data_center = False
    SUPPLIERS = ("luna_residential","iproyal_residential",)


    def createIproyalOrder(self, country_id: int, *, num: int = 1, timeout: int = 30) -> int:
        url = f"https://dash-api.proxy302.com/api/api/iproyal/order/{country_id}/{num}"
        logger.info("Entering createIproyalOrder function.")
        # url = "https://dashboard.iproyal.com/api/servers/proxies/reseller/orders"
        # headers = {"X-Access-Token": f"Bearer {constants.IPROYAL_TOKEN}", "Content-Type": "application/json"}
        # data = json.dumps({"productId": 9, "planId": 22, "locationId": location_id, "quantity": num,"discountCoupon":"adswave"})
        try:
            resp = requests.post(url, timeout=timeout)
            logger.debug(f"Response: {resp.text}")
            resp.raise_for_status()
            res = resp.json()
            logger.debug(f"JSON response: {res}")
            order_id = res.get("data",{}).get("data",{}).get("id")
            logger.info(f"Order created successfully. Order ID: {order_id}")
            return order_id
        except Exception as e:
            resp_text = resp.text if 'resp' in locals() else ''
            logger.error(f"Failed to create order. Error: {e} Response: {resp_text}")
            logger.info(traceback.format_exc())
            return 0

    def noProxyDo(self):
        iproyal_location = get_iproyal_location_by_country_id(country_id=self.country[0])
        proxies_num = get_proxy_num(country_id=self.country[0], source_in=("iproyal_residential",))
        if not iproyal_location or proxies_num is None:
            return self.returnError(constants.ERR_NEW_PROXY_FAILED)
        if (0 <= iproyal_location["ceiling"] <= proxies_num):
            try:
                country = get_country_by_id(self.country[0])
                constants.feishu.sendText(f"IProyal location:{country['code']}  达到上限{iproyal_location['ceiling']} ")
            except:
                traceback.print_exc()
            return self.returnError(constants.ERR_NEW_PROXY_FAILED)
        if self.payway.get("days",0) < 30:
            return self.returnError(constants.ERR_NEW_PROXY_FAILED)
        location_id = iproyal_location["location_id"]
        pay_order_id = self._pay(self.payway)
        if not pay_order_id:  # pay error, return balance not
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH)
        iproyal_order_id = self.createIproyalOrder(country_id=self.country[0],num=1)
        if not iproyal_order_id:
            self._refund(
                refund_payway=self.getRefundPrivateStaticIPPayway(is_data_center=self._is_data_center),
                user_token_id=0,
                pay_order_id=pay_order_id,
            )
            return self.returnError(constants.ERR_NEW_PROXY_FAILED)
        token_username, token_password = randomString(8), randomString(16)
        url = random.choice(constants.PROXY_SERVERS_LIST)
        pi_id = add_proxy_ips(
            status=3,
            online=True,
            health=True,
            country_id=self.country[0],
            # state_id=location['state_id'], city_id=location['city_id'],
            source="iproyal_residential",
            forward_port=0,
            port=0,
            username="",
            password="",
        )
        if (
            not save_iproyal_order(
                iproyal_order_id=iproyal_order_id,
                location_id=location_id,
                pi_id=pi_id if pi_id > 0 else 0,
                pay_order_id=pay_order_id,
            )
            or 0 >= pi_id
        ):
            self._refund(
                refund_payway=self.getRefundPrivateStaticIPPayway(is_data_center=self._is_data_center),
                user_token_id=0,
                pay_order_id=pay_order_id,
            )
            return self.returnError(constants.ERR_NEW_PROXY_FAILED)
        supplier = get_supplier_by_name("iproyal_residential")
        token = add_user_token(
            uid=self.uid,
            url=url,
            username=token_username,
            passwd=token_password,
            pi_id=pi_id,
            network=self.network,
            life_time=self.payway["days"],
            is_static=True,
            is_data_center=False,
            ps_id=supplier["id"],
        )  # save a token with pi_id=0, means proxy is creating
        if 0 >= token:
            self._refund(
                refund_payway=self.getRefundPrivateStaticIPPayway(is_data_center=self._is_data_center),
                user_token_id=token,
                pay_order_id=pay_order_id,
            )
            return self.returnError(constants.ERR_NEW_PROXY_FAILED)
        # set_order_user_token_id(pay_order_id, token)
        if self.payway["days"] >= 30:
            u = get_user_by_uid(self.uid)
            constants.feishu.sendText(
                f"用户:{u['email']}  购买的api生成的IProyal代理 {self.payway['days']}天\n" f"对应的IProyal订单号:#{iproyal_order_id}"
            )
        # 插入记录
        self.insert_traffic(token_id=token,pay_order_id=pay_order_id,order_id=iproyal_order_id)
        return self.returnSucceed(
            {
                "info": "代理生成中, 请等待5~10分钟。"
                if self.lang in ("cn", "zh")
                else "The proxy is being generated, please wait 5~10 minutes."
            }
        )

    def getBuyPrivateStaticIPPayway(self, payway_id: int) -> Optional[dict]:
        payway = get_payway_by_id(payway_id)
        return None if not payway or payway["payway"] != "buy_residential_private_static_ip" else payway
