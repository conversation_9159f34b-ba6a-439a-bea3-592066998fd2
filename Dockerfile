# 使用官方 Python 基础镜像 (Debian slim)
FROM python:3.9.7-slim

# 更新包列表并安装必要的构建工具和依赖项
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        libopenblas-dev \
        gfortran \
        cargo \
        rustc \
        cmake \
        libstdc++6 \
        libgcc1 \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 将当前目录中的内容复制到容器的 /app 目录
COPY . /app

# 使用阿里云的 pip 镜像安装依赖包
RUN pip install --no-cache-dir -r requirements.txt

# 定义容器启动时运行的命令
CMD ["celery", "-A", "app", "worker", "-c","20","-l","info"]