import random

import abc
from models.user import get_user_by_uid
from src.base import MakeProxyBase, ByIP, ByArea
from models.proxy import *
import pandas as pd


class SuperSell(MakeProxyBase):

    def __init__(self):
        self.user = get_user_by_uid(self.uid)
        self.machines_suppliers = {machine['id']: machine['suppliers'] for machine in get_all_machines()}
        self.sell_setting_of_user = get_sell_setting("user")
        self.sell_setting_of_911 = get_sell_setting("911")
        self.is_more_sell = True

    def _superSellIsTurnOn(self) -> bool:
        if self.sell_setting_of_user and self.sell_setting_of_user['on_off']:
            log.info(f"[SUPER_SELL] super sell turn-on")
            return True
        log.info(f"[SUPER_SELL] super sell turn-off")
        return False

    def _userIsUseSupperSell(self) -> bool:
        if not self.user or self.user['super_sell'] == 0:  # 该用户设置为不需要超卖
            log.info(f"[SUPER_SELL] uid:{self.uid} is not use supper sell")
            return False
        return True

    def _superSellProbability(self) -> bool:
        prob = float(self.sell_setting_of_user['probability'])
        weights = [1, 0] if prob > 1 else [0, 1] if prob < 0 else [prob, 1 - prob]
        into_super_sell = random.choices([True, False], weights=weights, k=1)[0]
        log.info(f"[SUPER_SELL] super sell probability:{prob}  uid:{self.uid}  result => {into_super_sell}")
        if not into_super_sell:
            return False
        return True

    def _isNotNewUser(self) -> bool:
        now_ts = int(time.time())
        if not self.user or now_ts - self.user['created_on'] <= self.sell_setting_of_user['how_long'] * 60 * 60:
            log.info(f"[SUPER_SELL] uid:{self.uid} user not enough super sell day")
            return False
        user_new_proxy_amount = get_user_new_proxy_amount(self.uid)
        if user_new_proxy_amount <= self.sell_setting_of_user['how_many']:
            log.info(f"[SUPER_SELL] uid:{self.uid} user not enough super sell amount")
            return False
        return True

    def choose(self) -> int:
        if not self._superSellIsTurnOn() or not self._userIsUseSupperSell() \
                or self._superSellProbability() and self._isNotNewUser():
            return 0
        proxies_df = self.search()
        if proxies_df.empty:
            return 0
        for i, row in proxies_df.iterrows():
            if len(get_proxy_using_condition(row['id'])) <= self.sell_setting_of_911['how_many']:
                return row['id']
        return 0

    @abc.abstractmethod
    def search(self) -> pd.DataFrame:
        pass


class SuperSellByIP(SuperSell, ByIP):

    def search(self) -> pd.DataFrame:
        if self.fuzzy_query:
            return pd.DataFrame(get_available_ip_fuzz_query(self.ip_addr))
        return pd.DataFrame(get_available_ip(int(self.ip_addr)))


class SuperSellByArea(SuperSell, ByArea):

    def search(self) -> pd.DataFrame:
        return pd.DataFrame(get_conform_area_proxy(country_id=self.country[0],
                                                   state_id=self.state[0],
                                                   city_id=self.city[0],uid=self.uid))


