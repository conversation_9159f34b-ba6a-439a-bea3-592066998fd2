from app import app
from src import *
from datetime import datetime
from log import get_logger
from src.make_proxy import MakeDynamicIPProxyFromPool

log = get_logger(__name__)

@app.task
def refresh_machines(machine_id=None):
    """
    刷新Windows server机器的缓存数据 (内部使用)
    :param machine_id:  :type:int   要指定刷新的一台机器的ID， default:None->刷新所有机器的缓存
    """
    if machine_id:
        refresh_machine_condition(machine_id)
    else:
        refresh_machines_use_condition()


@app.task
def make_proxy_by_ip(ip_addr, result_same_area=False, protocol="http", uid=None, machine_id=None,
                     use_more_sell=True, use_free_911=True, use_cn=True, use_911=True, lang:str='us') -> dict:
    """
    通过IP地址(模糊的IP地址)生成IP
    :param lang:                :type: str          返回提示的语言默认'us'
    :param ip_addr:             :type: str | int    IP地址，可使用*进行模糊搜索
    :param result_same_area:    :type: bool         在没有直接对应的IP地址的情况下是否返回IP地址对应地区的IP代理（default:False）,IP地址模糊查询时此参数无效
    :param protocol:            :type: str          网络协议类型,可选: None, http(https), socks(socket/socks5)，default:http
    :param uid:                 :type: int          用户ID。  default:None(内部使用 uid=0) 创建测试用的token,
     ----- 内部测试使用 -----
    :param machine_id:          :type: int          如果要用到911代理指定使用那台机器的机器ID (待开发)
    :param use_more_sell:       :type: bool         是否开通多卖， default: True
    :param use_free_911:        :type: bool         是否使用免费的二次911代理， default: True
    :param use_cn:              :type: bool         是否使用中国代理， default: True
    :param use_911:             :type: bool         是否使用911代理， default: True
    ------------------------
    :return:                    :type: dict          data.token_id 生成代理后对应的token ID
    """

    start_time = datetime.now()
    log.info(f"[make_proxy_by_ip] Task started at {start_time}.")

    make_proxy = MakeDynamicIPProxy(uid, lang=lang)
    # make_proxy.set_use(use_more_sell=use_more_sell, use_free_911=use_free_911, use_cn=use_cn, use_911=use_911)
    make_proxy.set_use(use_911=use_911)
    make_proxy.use_machine_id = machine_id
    log.info(f"[make_proxy_by_ip] Attempting to generate IP for IP address: {ip_addr}.")

    result = make_proxy.byIP(ip_addr, result_same_area, protocol)

    end_time = datetime.now()
    log.info(f"[make_proxy_by_ip] Task completed in {end_time - start_time}. Result: {result}")
    return result


@app.task
def make_proxy_by_area(country=None, state=None, city=None, protocol="http", uid=None, machine_id=None,
                       use_more_sell=True, use_free_911=True, use_cn=True, use_911=True, lang:str="us") -> dict:
    """
    通过地区生成IP
    :param lang:            :type: str          返回提示的语言默认'us'
    :param country:         :type: str | int    国家，可以使用country数据库中的id或者国家的缩写  default: None(0):随机
    :param state:           :type: str | int    省/州，可以使用state数据库中的id或者省/州的缩写  default: None(0):随机
    :param city:            :type: str | int    城市，可以使用city数据库中的id或者城市的英文  default: None(0):随机
    :param protocol:        :type: str          网络协议类型,可选: None, http(https), socks(socket/socks5)，default:http
    :param uid:             :type: int          用户ID。  default:None(内部使用 uid=0) 创建测试用的token,
     ----- 内部测试使用 -----
    :param machine_id:      :type: int          如果要用到911代理指定使用那台机器的机器ID (待开发)
    :param use_more_sell:   :type: bool         是否开通多卖， default: True
    :param use_free_911:    :type: bool         是否使用免费的二次911代理， default: True
    :param use_cn:          :type: bool         是否使用中国代理， default: True
    :param use_911:         :type: bool         是否使用911代理， default: True
    ------------------------
    :return:                :type: dict          生成代理后对应的token ID
    """
    start_time = datetime.now()
    log.info(f"[make_proxy_by_area] Task started at {start_time}.")

    make_proxy = MakeDynamicIPProxyFromPool(uid, lang=lang)
    # make_proxy.set_use(use_more_sell=use_more_sell, use_free_911=use_free_911, use_cn=use_cn, use_911=use_911)
    make_proxy.set_use(use_911=use_911)
    make_proxy.use_machine_id = machine_id

    log.info(f"[make_proxy_by_area] Attempting to generate IP for country={country}, state={state}, city={city}.")

    result = make_proxy.byArea(country, state, city, protocol)

    end_time = datetime.now()
    log.info(f"[make_proxy_by_area] Task completed in {end_time - start_time}. Result: {result}")
    return result



@app.task
def show_tokens(uid=None, after_ts=None, before_ts=None, status="") -> list:
    """
    展示用户拥有的动态代理
    :param uid:         :type: int  用户ID， default:None(内部使用 显示所有用户的)
    :param after_ts     :type: int  筛选时间(时间戳)之后的token数据
    :param before_ts    :type: int  筛选时间(时间戳)之前的token数据
    :param status       :type: str  根据代理状态过滤，default: "", 有效值有 'normal', 'expired', 'unhealthy'。非有效值显示全部
    :return:            :type: list 返回token的列表
    """
    return SearchDynamicProxy(uid).tokens(after_ts, before_ts, status)


@app.task
def search_token(username:str, password:str, uid=None) -> dict:
    """
    通过token 的username和password查找对应动态代理的token数据
    :param username:    :type: str  token的用户名
    :param password:    :type: str  token的密码
    :param uid:         :type: int  用户ID， default:None(内部使用 显示所有用户的)
    :return:            :type: dict token的具体详细数据，如果有多条相同的token只返回最新的一条
    """
    return SearchDynamicProxy(uid).byTokenUsername(username, password)