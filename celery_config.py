# 设置时区，默认为UTC时间
# CELERY_TIMEZONE = "Asia/Shanghai"

# 导入指定的任务模块
CELERY_IMPORTS = (
    'tasks.test',
    'tasks.virtual_machine',
    'tasks.user_token',
    'tasks.dynamic_ip_proxy',
    'tasks.static_ip_proxy',
    'tasks.dynamic_traffic_proxy',
    'tasks.static_traffic_proxy',
    'tasks.static_data_center_traffic_proxy',
    'tasks.upload_file'
)

# 任务失败或超时自动确认，默认为True
CELERY_ACKS_ON_FAILURE_OR_TIMEOUT=False
# 任务完成之后再确认
CELERY_ACKS_LATE=True
# worker进程崩掉之后拒绝确认
CELERY_REJECT_ON_WORKER_LOST=True