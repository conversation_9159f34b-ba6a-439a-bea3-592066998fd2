import queue
import string
import random
import ipaddress
import threading
import time

import requests
from typing import Union, Tuple
import constants
from libs import torndb
from models.area import *

import uuid

from log import get_logger

log = get_logger(__name__)

def randomString(length: int, number: bool = True, uppercase: bool = True, lowercase: bool = True) -> str:
    if type(length) != int:
        raise TypeError("length must be int")
    scope = ""
    if number:
        scope += string.digits
    if uppercase:
        scope += string.ascii_uppercase
    if lowercase:
        scope += string.ascii_lowercase
    if scope:
        return "".join(random.choice(scope) for _ in range(length))
    else:
        raise ValueError("number / uppercase / lowercase not all False")


def probability(prob: float) -> bool:
    weights = [1, 0] if prob > 1 else [0, 1] if prob < 0 else [prob, 1 - prob]
    return random.choices([True, False], weights=weights, k=1)[0]


def ip_verify(
    ip_addr: Union[int, str, ipaddress.IPv4Address, ipaddress.IPv6Address]
) -> Union[ipaddress.IPv4Address, ipaddress.IPv6Address]:
    """
    ip地址校验，并转成字符串形式的IP
    :param ip_addr:
    :return:
    """
    if type(ip_addr) in [ipaddress.IPv4Address, ipaddress.IPv6Address]:
        return ip_addr
    if type(ip_addr) == str and len(ip_addr.split("*")) > 1:
        return ip_addr
    _ip_addr = ipaddress.ip_address(ip_addr)
    return _ip_addr



def area_verify(country=None, state=None, city=None):
    """
    根据国家、州/省和城市信息，返回对应的地区 ID

    Args:
        country (Union[str, int, None]): 国家代码、国家 ID 或者 None
        state (Union[str, int, None]): 州/省代码、州/省 ID 或者 None
        city (Union[str, int, None]): 城市名称、城市 ID 或者 None

    Returns:
        ((int, str), (int, str, str), (int, str)): 包含国家、州/省和城市 ID 的元组
        如果传入的参数不正确，则会抛出 ValueError 异常

    Raises:
        ValueError: 传入的参数不正确，如国家/州/城市 ID 或代码不正确，或者某个参数为 None

    """
    # 处理国家信息
    if country is None or country == 0 or country == "" or country == "0":
        # 如果国家信息为空或者为 0，则返回默认值
        return (0, ""), (0, "", ""), (0, "")
    # 如果国家信息不为空，则获取国家对象
    country_obj = get_country_by_id(int(country)) if str(country).isdigit() else get_country_by_code(country)
    if not country_obj:
        raise ValueError("国家代码或 ID 不正确")
    _country = (country_obj["id"], country_obj["code"])

    # 处理州/省信息
    if state is None or state == 0 or state == "" or state == "0":
        # 如果州/省信息为空或者为 0，则返回国家信息
        return _country, (0, "", ""), (0, "")
    # 如果州/省信息不为空，则获取州/省对象
    state_obj = get_state_by_id(int(state)) if str(state).isdigit() else get_state_by_code(_country[0], state)
    if not state_obj:
        raise ValueError("州/省代码或 ID 不正确")
    _state = (state_obj["id"], state_obj["code"], state_obj["name"])

    # 处理城市信息
    if city is None or city == 0 or city == "" or city == "0":
        # 如果城市信息为空或者为 0，则返回国家和州/省信息
        return _country, _state, (0, "")
    # 如果城市信息不为空，则获取城市对象
    city_obj = get_city_by_id(int(city)) if str(city).isdigit() else get_city_by_name(_state[0], city)
    if not city_obj:
        raise ValueError("城市代码或名称不正确")
    _city = city_obj["id"], city_obj["name"]

    return _country, _state, _city


def get_ip_area(ip_addr, timeout=10) -> Union[Tuple[int, str], None]:
    """
    获取 IP 地址的地区信息

    Args:
        ip_addr (str): IP 地址
        timeout (int): 请求超时时间，单位为秒

    Returns:
        ((int, str), (int, str), (int, str)): 包含国家、州/省和城市信息的元组，如 ((1, 'US'), (2, 'CA'), (3, 'Los Angeles'))
                                               如果获取失败，则返回 None

    Raises:
        requests.exceptions.RequestException: 请求异常，如网络连接失败等
        ValueError: IP 地址格式不正确

    """
    try:
        ip_addr = ipaddress.ip_address(ip_addr)  # 尝试将输入的 IP 地址转换成 IPv4 或 IPv6 对象
    except ValueError:
        return None  # 如果转换失败，则说明输入的不是有效的 IP 地址，返回 None

    if ip_addr.is_private:  # 如果 IP 地址是私有地址，则返回 None，因为私有地址没有地区信息
        return None

    try:
        url = constants.IP_INFO_URL.format(_ip=str(ip_addr))  # 构造请求 URL
        log.info(f"请求 URL: {url}")
        res = requests.get(url=url, params={"token": constants.IP_INFO_TOKEN}, timeout=timeout)  # 发送请求
        res.raise_for_status()  # 如果请求失败，则会抛出异常
        res_data = res.json()  # 尝试将响应的 JSON 数据转换成 Python 对象
    except (requests.exceptions.RequestException, ValueError):
        log.exception("请求 IP 地址信息出错")
        return None  # 如果请求失败，则返回 None

    country = get_country_by_code(res_data.get("country", ""))  # 获取国家信息
    if not country:
        return None

    state = get_state_by_name(country["id"], res_data.get("region", ""))  # 获取州/省信息
    if not state:
        return (country["id"], country["code"]), (0, ""), (0, "")

    city = get_city_by_name(state["id"], res_data.get("city", ""))  # 获取城市信息
    if not city:
        return (country["id"], country["code"]), (state["id"], state["code"]), (0, "")

    return (country["id"], country["code"]), (state["id"], state["code"]), (city["id"], city["name"])  # 返回完整的地区信息


def testProxy(proxy, *, test_urls=None, timeout=20) -> bool:
    """
    测试代理的可用性
    :param proxy:   代理
    :return:        是否可用
    """
    test_urls = test_urls or [
        "https://ipinfo.io/",
        "https://www.google.com.hk/",
        "https://www.google.com/",
        "http://myip.ipip.net/",
    ]
    proxies = {"http": proxy,"https":proxy}
    success = False
    trace_id = str(uuid.uuid4())

    with requests.Session() as session:
        session.proxies = proxies

        for i, test_url in enumerate(test_urls):
            try:
                log.info(f"TraceID:{trace_id} 第 {i+1} 次请求  url:{test_url}  proxy:{proxy}")
                res = session.get(url=test_url, timeout=timeout)
                log.info(f"TraceID:{trace_id} 响应状态码:{res.status_code}")
                if res.status_code // 100 == 2:
                    success = True
                    break
            except requests.exceptions.Timeout as e:
                log.warning(f"TraceID:{trace_id} 请求超时: {str(e)}，url:{test_url}, proxy:{proxy}")
            except requests.exceptions.RequestException as e:
                log.warning(f"TraceID:{trace_id} 请求失败: {str(e)}，url:{test_url}, proxy:{proxy}")
            if i >= 2:
                log.warning(f"TraceID:{trace_id} 请求次数超过 3 次，终止尝试请求，url:{test_url}, proxy:{proxy}")
                break

    return success


def languageChecker(lang: str, default: str = "us") -> str:
    """
    统一判断语言
    :param lang:    语言
    :param default: 语言不在支持范围内的默认值， 如果这里设置为空则遇到不支持的怨言是会报TypeError错
    :return:
    """
    if lang.lower() in ("cn", "zh-cn", "zh"):
        return "cn"
    elif lang.lower() in ("us", "en", "en-us"):
        return "us"
    else:
        if default:
            return default
        raise TypeError(" language ")

from concurrent.futures import ThreadPoolExecutor

class DBS():

    def __init__(self,min_size=1,max_size=5,config=None):
        self.min_size = min_size
        self.max_size = max_size
        self.config = config
        self._pool = list()
        # ThreadPoolExecutor(1).submit(self._check)

    def _check(self):
        while 1:
            time.sleep(10)
            con = self.get_conn()
            try:
                row = con.get("select 1")
                self._pool.append(con)
            except:
                pass

    def __len__(self):
        return len(self._pool)

    def create_pool(self):
        for i in range(self.min_size):
            self._pool.append(torndb.Connection(**self.config))
        return self

    def get_conn(self):
        if len(self._pool) == 0:
            self.create_pool()
        return self._pool.pop()

    def release(self,conn):
        if len(self._pool) < self.max_size:
            self._pool.append(conn)
        else:
            conn.close()

    def query(self,*args,**kwargs):
        conn = self.get_conn()
        res = conn.query(*args,**kwargs)
        self.release(conn)
        return res

    def get(self,*args,**kwargs):
        conn = self.get_conn()
        res = conn.get(*args,**kwargs)
        self.release(conn)
        return res

    def execute(self,*args,**kwargs):
        conn = self.get_conn()
        res = conn.execute(*args,**kwargs)
        self.release(conn)
        return res

    def execute_rowcount(self,*args,**kwargs):
        conn = self.get_conn()
        res = conn.execute_rowcount(*args,**kwargs)
        self.release(conn)
        return res


class MysqlConnPool(object):

    def __init__(self, conf, max_conns=2):
        self.idle_conn = queue.Queue()
        self.pool_size = 0
        self.max_conns = max_conns
        self.conn_params = conf
        self.poll_size_mutex = threading.Lock()

    def _get_conn_from_pool(self) ->torndb.Connection:
        if self.idle_conn.empty() and self.pool_size < self.max_conns:
            conn = torndb.Connection(**self.conn_params)
            self.poll_size_mutex.acquire()
            self.pool_size += 1
            self.poll_size_mutex.release()
            return conn
        return self.idle_conn.get()
    # 查询函数
    def query(self, sqlcommand,*args, **kwargs):
        conn = self._get_conn_from_pool()
        res = conn.query(sqlcommand,*args, **kwargs)
        self.idle_conn.put(conn)
        return res

    def get(self,sqlcommand,*args, **kwargs):
        conn = self._get_conn_from_pool()
        res = conn.get(sqlcommand, *args, **kwargs)
        self.idle_conn.put(conn)
        return res

    def execute_rowcount(self, sqlcommand, *args, **kwargs):
        conn = self._get_conn_from_pool()
        res = conn.execute_rowcount(sqlcommand, *args, **kwargs)
        self.idle_conn.put(conn)
        return res

    # 执行+查询函数
    def execute(self,sqlcommand, *args, **kwargs):
        conn = self._get_conn_from_pool()
        res = conn.execute(sqlcommand,*args, **kwargs)
        self.idle_conn.put(conn)
        return res


if __name__ == '__main__':
    testProxy("socks5://14a8c73364f30:206bbc3932@187.17.215.160:12324")