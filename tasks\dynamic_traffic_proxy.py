from app import app
from src.make_proxy import MakeTrafficProxy


@app.task
def make_proxy_by_area(country=None, state=None, city=None, uid=None, lang="us", s=0, protocol="http") -> dict:
    """
    通过地区生成动态流量代理
    :param country:         :type: str | int    国家，可以使用country数据库中的id或者国家的缩写  default: None(0):随机
    :param state:           :type: str | int    省/州，可以使用state数据库中的id或者省/州的缩写  default: None(0):随机
    :param city:            :type: str | int    城市，可以使用city数据库中的id或者城市的英文  default: None(0):随机
    :param uid:             :type: int          用户ID。  default:None(内部使用 uid=0) 创建测试用的token,
    :param lang:            :type: str          语言。 'us':英文,'zh':中文  default:‘us’ 参数是不支持语言的时候为英文
    :return:
    """
    return MakeTrafficProxy(is_static=False, uid=uid, lang=lang).by_area(country, state, city, s=s, protocol=protocol)


@app.task
def make_proxy_api_proxy(only_country=False, uid=None, lang="us", protocol="http",s=2) -> dict:
    """
    生成动态流量API代理
    :param only_country:    :type: bool     只是使用国家。 default:False, 可以使用州和城市
    :param uid:             :type: int      用户ID。  default:None(内部使用 uid=0) 创建测试用的token,
    :param lang:            :type: str      语言。 'us':英文,'zh':中文  default:‘us’ 参数是不支持语言的时候为英文
    :return:
    """
    return MakeTrafficProxy(is_static=False, uid=uid, lang=lang).make_api_proxy(only_country=only_country, protocol=protocol,s=s)


@app.task
def change_proxy_status(token_id: int, status_key: str, status_value: int, uid=None):
    """
    改变代理（user_token）的状态
    :param token_id:        :type: int  代理token的ID
    :param status_key:      :type: str  要改变的状态key
    :param status_value:    :type: int  要改变的状态值
    :param uid:             :type: int  用户ID， default:None(内部使用 不做用户验证)
    :return:
    """


@app.task
def show_tokens(uid=None, after_ts=None, before_ts=None, status="") -> list:
    """
    展示用户拥有的动态代理
    :param uid:         :type: int  用户ID， default:None(内部使用 显示所有用户的)
    :param after_ts     :type: int  筛选时间(时间戳)之后的token数据
    :param before_ts    :type: int  筛选时间(时间戳)之前的token数据
    :param status       :type: str  根据代理状态过滤，default: "", 有效值有 'normal', 'expired', 'unhealthy'。非有效值显示全部
    :return:            :type: list 返回token的列表
    """


@app.task
def search_token(username: str, password: str, uid=None) -> dict:
    """
    通过token 的username和password查找对应动态代理的token数据
    :param username:    :type: str  token的用户名
    :param password:    :type: str  token的密码
    :param uid:         :type: int  用户ID， default:None(内部使用 显示所有用户的)
    :return:            :type: dict token的具体详细数据，如果有多条相同的token只返回最新的一条
    """
