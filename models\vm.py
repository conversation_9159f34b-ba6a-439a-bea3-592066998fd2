from typing import Union

import ipaddress

from datetime import datetime, date

import os

import time
import traceback
import db
import pytz
from dateutil.relativedelta import relativedelta
from log import get_logger

log = get_logger(__name__)


def count_user_having_vm(uid) -> int:
    return db.mysql.get(
        """
        SELECT COUNT(1) FROM t_user_tokens WHERE uid = %s AND payway_id != 0 AND deleted_on = 0  AND expired = 0
    """,
        uid,
    )["COUNT(1)"]


def get_vm_level_by_id(level_id: int) -> dict:
    return db.mysql.get(""" select * from t_vm_levels WHERE  deleted_on = 0 AND id = %s """, level_id)


def get_vm_location_by_country_id(country_id: int = 0) -> dict:
    return (
        db.mysql.get(
            """-- sql
        SELECT * FROM t_vm_locations WHERE country_id = %s AND deleted_on = 0 ORDER BY RAND() LIMIT 1 
    """,
            country_id,
        )
        if country_id
        else db.mysql.get(
            """-- sql
        SELECT * FROM t_vm_locations WHERE deleted_on = 0 ORDER BY RAND() LIMIT 1
    """
        )
    )


def save_machine(
    ip_addr: Union[str, int], ps_id: int, port: int, vm_name: str = "", public_ip: str = "", db_session=None
) -> int:
    db_mysql = db_session or db.mysql
    try:
        ip_addr, t = ipaddress.ip_address(ip_addr), int(time.time())
        ip_obj = db_mysql.get("SELECT * FROM t_ips WHERE CAST(CONV(ip_address, 16, 10) AS SIGNED) = %s", int(ip_addr))
        ip_id = (
            ip_obj["id"]
            if ip_obj
            else db_mysql.execute(
                """
            INSERT INTO t_ips(ip_addr, ip_address, created_on, modified_on, deleted_on) 
            VALUES (%s, HEX(INET6_ATON(%s)), %s, %s, %s)
        """,
                int(ip_addr) if type(ip_addr) == ipaddress.IPv4Address else 0,
                str(ip_addr),
                t,
                t,
                0,
            )
        )
        machine_id = db_mysql.execute(
            """
            INSERT INTO t_proxy_machines (ip_id, ps_id, created_on, modified_on, deleted_on,
            is_linux, forward_port_start, forward_port_end, fault, vm_name, public_ip) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """,
            ip_id,
            ps_id,
            t,
            t,
            0,
            1,
            port,
            port,
            0,
            vm_name,
            public_ip,
        )
        return machine_id
    except:
        traceback.print_exc()
        log.error(traceback.format_exc())
        return 0


def get_vm_tokens(uid: int = None, after_ts: int = None, before_ts: int = None):
    before_ts = int(time.time()) if before_ts is None else int(before_ts)  # 默认 当前时间
    after_ts = (
        int(after_ts)
        if after_ts is not None
        else int(time.mktime((datetime.today() + relativedelta(months=-12)).timetuple()))
    )  # 默认 12个月
    if uid:
        sql = """-- sql
            SELECT t_user_tokens.id, t_user_tokens.created_on, t_user_tokens.pi_id, remark, pm_id,
            CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr,  t_user_tokens.is_static, url, 
            t_user_tokens.username, t_user_tokens.passwd, network, expired, vm_level_id,
            t_geo_country.code as country_code, t_geo_country.name as country_name, 
            online, status, health, refund, life_time, vm_name FROM t_user_tokens 
            LEFT JOIN t_proxy_ips ON t_user_tokens.pi_id = t_proxy_ips.id 
            LEFT JOIN t_geo_country ON t_geo_country.id = t_proxy_ips.country_id
            LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id 
            LEFT JOIN t_payways ON t_payways.id = payway_id
            LEFT JOIN t_proxy_machines ON t_proxy_ips.pm_id = t_proxy_machines.id 
            WHERE (%s OR t_user_tokens.uid = %s) AND t_proxy_ips.deleted_on = 0 AND payway_id != 0 
            AND t_user_tokens.deleted_on = 0 AND t_user_tokens.created_on BETWEEN %s AND %s
            ORDER BY t_user_tokens.created_on DESC 
        """
    else:
        sql = """-- sql
            SELECT t_user_tokens.id, t_user_tokens.created_on, t_user_tokens.pi_id, remark, pm_id,
            CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr,  t_user_tokens.is_static, url, 
            t_user_tokens.username, t_user_tokens.passwd, network, expired, vm_level_id, email, 
            t_vm_levels.name AS level_name, cpu, ram, storage, days, 
            t_geo_country.code as country_code, t_geo_country.name as country_name, 
            online, status, health, refund, life_time, vm_name FROM t_user_tokens 
            LEFT JOIN t_proxy_ips ON t_user_tokens.pi_id = t_proxy_ips.id 
            LEFT JOIN t_geo_country ON t_geo_country.id = t_proxy_ips.country_id
            LEFT JOIN t_users ON t_users.uid = t_user_tokens.uid
            LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id 
            LEFT JOIN t_payways ON t_payways.id = payway_id
            LEFT JOIN t_proxy_machines ON t_proxy_ips.pm_id = t_proxy_machines.id 
            LEFT JOIN t_vm_levels  on t_vm_levels.id = vm_level_id
            WHERE (%s OR t_user_tokens.uid = %s) AND t_proxy_ips.deleted_on = 0 AND payway_id != 0 
            AND t_user_tokens.deleted_on = 0 AND t_user_tokens.created_on BETWEEN %s AND %s
            ORDER BY t_user_tokens.created_on DESC 
        """
    return db.mysql.query(sql, uid is None or uid == 0, uid, after_ts, before_ts)


def get_vm_by_token_id(token_id: int, supplier: str = "win_vm"):
    return db.mysql.get(
        """
        SELECT t_user_tokens.id, uid, pi_id, vm_name, payway_id, 
        expired, status, health, online, t_user_tokens.is_static FROM t_user_tokens 
        LEFT JOIN t_proxy_ips ON t_proxy_ips.id = t_user_tokens.pi_id
        LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id
        LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id
        WHERE t_user_tokens.deleted_on = 0 AND t_proxy_suppliers.name = %s AND t_user_tokens.id = %s
    """,
        supplier,
        token_id,
    )


def set_vm_auto_renew(token_id: int, on_off: bool, payway_id: int) -> bool:
    try:
        effect = db.mysql.execute_rowcount(
            """
            UPDATE t_user_tokens SET is_static = %s, payway_id = %s WHERE deleted_on = 0 AND id = %s
        """,
            on_off,
            payway_id,
            token_id,
        )
        return effect > 0
    except:
        traceback.print_exc()
        return False
