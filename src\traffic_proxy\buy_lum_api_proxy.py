import abc
from typing import Union
import json
import requests
from models.proxy import *
from src.base import MakeProxyBase, ByProxyID, ByArea
from src.exceptions import *
from utils.functions import randomString
import constants
from log import get_logger

log = get_logger(__name__)

class BuyLumAPIProxy(MakeProxyBase):
    def __init__(self, **kwargs):
        self.supplier_id = 0
        self.network = kwargs.get("network", "http")
        self.s = kwargs.get("s", 2)

    def choose(self) -> int:
        lum_account = self.chooseLumAccount()
        if not lum_account:
            return 0
        machine_objs = get_machine_by_id(lum_account['pm_id'])
        if not machine_objs:
            self.set_err(constants.ERR_MACHINE_DEFICIENCY)
            return 0
        machine_obj = machine_objs
        self.supplier_id = machine_obj['ps_id']


        self.la_id, area = lum_account['id'], (-2, 0, 0) if lum_account['only_country'] else (-2, -2, -2)
            
        if lum_account['type'] == 4:
            lum_account['username'] = "user-"+lum_account['username']
            lum_account['username'] += '-sessionduration-10'
        return add_proxy_ips(ip_id=self.chooseIPAddressID(lum_account['username'], lum_account['password']),
                             pm_id=machine_obj['id'], forward_port=machine_objs['forward_port_start'] if self.network == 'http' else machine_obj['socks5_port'],
                             country_id=area[0], state_id=area[1], city_id=area[2],
                             online=True, status=True, health=True,
                             username=lum_account['username'], password=lum_account['password'])

    @abc.abstractmethod
    def chooseLumAccount(self) -> Union[dict, None]:
        """
        实现这个方法 选择需要的luminati账号
        :return: luminati account obj   没有对应账号返回 None
        """
        return None

    @abc.abstractmethod
    def chooseIPAddressID(self, username:str, password:str) -> int:
        """
        实现这个方法 此代理对应显示的IP地址，返回的是IP对应t_ips中的ID，不需要显示返回0
        :param username:    luminati's zone-username
        :param password:    luminati's zone-password
        :return:  t_ips.id
        """
        return 0


class BuyDynamicLumAPIProxy(BuyLumAPIProxy):
    """
    生成动态API代理
    """
    def __init__(self, **kwargs):
        self._only_country = False
        self.network = kwargs.get("network", "http")
        self.s = kwargs.get("s", 2)

    @property
    def is_only_country(self):
        return self._only_country

    @is_only_country.setter
    def is_only_country(self, only_country:bool):
        self._only_country = bool(only_country)

    def chooseLumAccount(self) -> dict:
        lum_account_obj = get_luminati_account(self._only_country, is_static=False,s=self.s,is_api=True)
        return lum_account_obj

    def chooseIPAddressID(self, username: str, password: str) -> int:
        return 0


class BuyStaticLumAPIProxy(BuyLumAPIProxy):
    """
    生成静态API代理
    """
    def chooseLumAccount(self) -> dict:
        lum_account_obj = get_luminati_account(only_country=True, is_static=True)
        return lum_account_obj

    def chooseIPAddressID(self, username: str, password: str) -> int:
        return 0


class BuyDataCenterLumAPIProxy(BuyLumAPIProxy):
    """
    生成数据中心静态代理
    """

    def chooseLumAccount(self) -> dict:
        lum_account_obj = get_luminati_account(only_country=True, is_static=True, is_data_center=True)
        return lum_account_obj

    def chooseIPAddressID(self, username: str, password: str) -> int:
        return 0