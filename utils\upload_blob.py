# -*- coding: utf-8 -*-
# @Time    : 2024/2/27 11:23
# <AUTHOR> hzx1994
# @File    : upload_blob.py
# @Software: PyCharm
# @description:
from urllib.parse import urlparse
from uuid import uuid4
from pathlib import Path
import hashlib
import requests
from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient,ContentSettings
import os
import mimetypes
import oss2
from nano_graphrag import GraphRAG
from pathlib import Path as pa
from openai import AsyncOpenAI
from hashlib import md5
from nano_graphrag.base import BaseVectorStorage
import aiohttp
import asyncio
from nano_graphrag._utils import logger
import numpy as np
from nano_graphrag._utils import compute_args_hash, wrap_embedding_func_with_attrs
import numpy as np
import json
WORKING_DIR = pa("/mnt").joinpath("azure").joinpath("kb").joinpath("rag_nano")
# 初始化BlobServiceClient
connect_str = "DefaultEndpointsProtocol=https;AccountName=proxyblob;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
blob_service_client = BlobServiceClient.from_connection_string(connect_str)


# 阿里云OSS的访问信息
access_key_id = 'LTAI5tKnWCy1Pmkboqna8fUn'
access_key_secret = '******************************'
endpoint = 'http://oss-cn-shenzhen.aliyuncs.com'
bucket_name = '302ai'

# 初始化OSS认证和Bucket

# 上传文件的函数
def upload_file(file_path, object_name):
    # 打开文件并上传到OSS
    with open(file_path, 'rb') as file_data:
        auth = oss2.Auth(access_key_id, access_key_secret)
        bucket = oss2.Bucket(auth, endpoint, bucket_name)
        bucket.put_object(object_name, file_data,headers={'Content-Type': 'application/octet-stream'})
    print(f"File {file_path} uploaded to {object_name} successfully.")
# 指定要上传到的容器名称
container_name = "gpt"

# 创建容器（如果尚未存在）
container_client = blob_service_client.get_container_client(container_name)
try:
    container_client.create_container()
except Exception as e:
    print(e)


def get_content_type(filename):
    # 获取文件的MIME类型和编码（如果有的话）
    content_type, _ = mimetypes.guess_type(filename)

    if filename.endswith("webp"):
        content_type='image/webp'

    if filename.endswith("md"):
        content_type = 'text/markdown;charset=utf-8'

    if content_type in ('text/plain','text/html'):
        content_type+=';charset=utf-8'

    # 如果无法确定MIME类型，则返回一个默认值，比如 'application/octet-stream'
    if content_type is None:
        content_type = 'application/octet-stream'

    return content_type


def upload_part(bucket, object_name, upload_id, part_number, part_data):
    result = bucket.upload_part(
        object_name,
        upload_id,
        part_number,
        part_data
    )
    print(f'已上传分片 {part_number}')
    return oss2.models.PartInfo(part_number, result.etag)

def oss_upload(bytes_data,bucket,object_name):
    total_size = len(bytes_data)
    part_size = oss2.determine_part_size(total_size, preferred_size=1 * 1024 * 1024)  # 可以调整 preferred_size

    # 初始化分片上传
    upload_id = bucket.init_multipart_upload(object_name).upload_id
    parts = []

    part_number = 1
    offset = 0

    # 将 bytes_data 按照 part_size 分割上传
    while offset < total_size:
        part_data = bytes_data[offset: offset + part_size]

        part_info =upload_part(bucket, object_name, upload_id, part_number, part_data)
        parts.append(part_info)

        offset += part_size
        part_number += 1

    # 完成分片上传
    result = bucket.complete_multipart_upload(object_name, upload_id, parts)
    print('分片上传完成。')

def update_from_url(url,blob_path,name):
    # resp = requests.get(url)
    blob_client = blob_service_client.get_blob_client(container=container_name, blob=f'{blob_path}/{name}')
    blob_client.upload_blob_from_url(url)

    file_data = requests.get(url).content
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)
    content_type = get_content_type(name)
    bucket.put_object(f"{blob_path}/{name}", file_data, headers={'Content-Type': content_type})

def upload(file,blob_path='imgs'):
    # 上传文件的路径
    local_file_name = file
    # local_file_path = os.path.join("path-to-file", local_file_name)
    p = Path(file)
    # 创建一个BlobClient对象
    blob_client = blob_service_client.get_blob_client(container=container_name, blob=f'{blob_path}/{p.name}')
    content_type = get_content_type(p.name)
    # 上传文件
    with open(local_file_name, "rb") as data:
        blob_client.upload_blob(data, overwrite=True, content_settings=ContentSettings(content_type=content_type))
    blob_url = blob_client.url
    os.remove(local_file_name)

    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)
    content_type = get_content_type(p.name)
    bucket.put_object(f"{blob_path}/{p.name}", data, headers={'Content-Type': content_type})


    print(f"Blob的URL是: {blob_url}")
    print(f"文件 {local_file_name} 上传成功到容器 {container_name} 中。")
def upload_blob_func(data: bytes = None, file_name=None, pre='imgs',proxy_blob=False,content_type=None):
    if not file_name:
        hs5 = hashlib.md5()
        hs5.update(data)
        name = hs5.hexdigest()
        file_name = f"{name}.png"

    blob_client = blob_service_client.get_blob_client(container=container_name, blob=f'{pre}/{file_name}')
    content_type = content_type or get_content_type(file_name)
    print(content_type)
    
    blob_client.upload_blob(data, overwrite=True, content_settings=ContentSettings(content_type=content_type,content_encoding="utf-8",content_length=len(data)))
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, endpoint, bucket_name)
    oss_upload(bytes_data=data,bucket=bucket,object_name=f"gpt/{pre}/{file_name}")

    if proxy_blob:
        blob_url = f"https://proxyblob.blob.core.windows.net/gpt/{pre}/{file_name}"
    else:
        blob_url = f"https://file.302ai.cn/gpt/{pre}/{file_name}"

    return {"blob_url":blob_url}

from nano_graphrag.prompt import PROMPTS
from nano_graphrag._splitter import SeparatorSplitter
def chunking_by_seperators(
    tokens_list: list[list[int]],
    doc_keys,
    tiktoken_model,
    overlap_token_size=128,
    max_token_size=1024,
    separator = None,
):
    print(PROMPTS["default_text_separator"])
    separator  = separator if separator else PROMPTS["default_text_separator"]
    splitter = SeparatorSplitter(
        separators=[
            tiktoken_model.encode(s) for s in separator
        ],
        chunk_size=max_token_size,
        chunk_overlap=overlap_token_size,
    )
    results = []
    for index, tokens in enumerate(tokens_list):
        chunk_token = splitter.split_tokens(tokens)
        lengths = [len(c) for c in chunk_token]

        # here somehow tricky, since the whole chunk tokens is list[list[list[int]]] for corpus(doc(chunk)),so it can't be decode entirely
        chunk_token = tiktoken_model.decode_batch(chunk_token)
        for i, chunk in enumerate(chunk_token):

            results.append(
                {
                    "tokens": lengths[i],
                    "content": chunk.strip(),
                    "chunk_order_index": i,
                    "full_doc_id": doc_keys[index],
                }
            )

    return results

#选择分割器以及设置每个chunk的切片阈值
async def select_textspilter(textsliter_name,max_token_size,chunk_overlap,separator):
    from functools import partial
    from nano_graphrag._op import chunking_by_token_size

    if textsliter_name == "chunk_size":
        custom_chunk_func = partial(chunking_by_token_size, max_token_size=max_token_size,overlap_token_size = chunk_overlap)
    
    elif textsliter_name == "separator":
        separator_list = list(separator.strip()) if separator else None
        custom_chunk_func = partial(chunking_by_seperators, max_token_size=max_token_size,overlap_token_size = chunk_overlap,separator=separator_list)

    return custom_chunk_func

async def get_llm_func(api_key, model, base_url,retry=3):
    async def llm_model_if_cache(
            prompt, system_prompt=None, history_messages=[],retry=retry, **kwargs
    ) -> str:
        openai_async_client = AsyncOpenAI(
            api_key=api_key, base_url=base_url
        )
        messages = []

        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        # Get the cached response if having-------------------
        hashing_kv = kwargs.pop("hashing_kv", None)
        messages.extend(history_messages)
        messages.append({"role": "user", "content": prompt})
        if hashing_kv is not None:
            args_hash = compute_args_hash(model, messages)
            if_cache_return = await hashing_kv.get_by_id(args_hash)
            if if_cache_return is not None:
                return if_cache_return["return"]
        # -----------------------------------------------------

        response = await openai_async_client.chat.completions.create(
            model=model, messages=messages, **kwargs
        )

        # Cache the response if having-------------------
        if hashing_kv is not None:
            await hashing_kv.upsert(
                {"args_hash": {"return": response.choices[0].message.content, "model": model}}
            )
        # -----------------------------------------------------

        msg = response.choices[0].message.content
        if msg.startswith("{"):
            try:
                json.loads(msg)
            except:
                if retry>0:
                    msg = await llm_model_if_cache(
                        prompt, system_prompt=system_prompt, history_messages=[],retry=retry-1, **kwargs
                    )
                    return msg
                else:
                    return None
        return msg

    return llm_model_if_cache


async def get_emb_func(api_key, base_url, model='text-embedding-ada-002',retry=2, **kwargs):
    dimension = kwargs.get("dimension",768)
    @wrap_embedding_func_with_attrs(embedding_dim=dimension, max_token_size=8192)
    async def emb_func(texts: list[str], retry=retry,**kwargs) -> np.ndarray:
        if not model.startswith("jina"):
            openai_async_client = AsyncOpenAI(api_key=api_key, base_url=base_url, **kwargs)
            response = await openai_async_client.embeddings.create(
                model=model, input=texts, encoding_format="float"
            )
            emb =  np.array([dp.embedding for dp in response.data])
            return emb

        url = f"{base_url.replace('/v1', '')}/jina/v1/embeddings"
        data = {
            "model": model,
            "embedding_type": "float",
            "input": texts,
            "dimensions": 768 if model == "jina-embeddings-v3" else None,
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        try:
            async with aiohttp.ClientSession() as s:
                async with s.post(url, headers=headers, json=data) as resp:
                    resp = await resp.json()
        except Exception as e:
            if retry <= 0:
                raise e
            return await emb_func(texts,retry-1, **kwargs)
        embs = [i["embedding"] for i in resp["data"]]
        emb = np.array(embs)
        return emb

    return emb_func
from dataclasses import dataclass

client = None
@dataclass
class MilvusStorge(BaseVectorStorage):
    @staticmethod
    def create_collection_if_not_exist(client, collection_name: str, **kwargs):
        if client.has_collection(collection_name):
            return
        # TODO add constants for ID max length to 32
        client.create_collection(
            collection_name, max_length=64, id_type="string", **kwargs
        )
    @staticmethod
    def create_partition_if_not_exist(client, collection_name: str, partition_name: str,**kwargs):
        if client.has_partition(collection_name,partition_name):
            return
        client.create_partition(collection_name, partition_name,**kwargs)
    def __post_init__(self):
        from pymilvus import MilvusClient
        import time
        global client
        self.loop = asyncio.get_running_loop()
        if client is None:
            client = MilvusClient("http://10.0.1.61:19530")
        self._client = client
        self._client.using_database('rag_nano')
        self._max_batch_size = self.global_config["embedding_batch_num"]
        namespace = "c_"+md5(str(self.global_config["working_dir"]).encode()).hexdigest()
        self.namespace = namespace
        # 使用时间戳生成唯一分区名称
        timestamp = int(time.time() * 1000)  # Milliseconds since epoch
        self.partition_name = f"p_{timestamp}"
        MilvusStorge.create_collection_if_not_exist(
            self._client,
            self.namespace,
            dimension=self.embedding_func.embedding_dim,
        )

    async def upsert(self, data: dict[str, dict]):
        logger.info(f"Inserting {len(data)} vectors to {self.namespace}")
        list_data = [
            {
                "id": k,
                **{k1: v1 for k1, v1 in v.items() if k1 in self.meta_fields},
            }
            for k, v in data.items()
        ]
        contents = [v["content"] for v in data.values()]
        batches = [
            contents[i : i + self._max_batch_size]
            for i in range(0, len(contents), self._max_batch_size)
        ]
        embeddings_list = await asyncio.gather(
            *[self.embedding_func(batch) for batch in batches]
        )
        embeddings = np.concatenate(embeddings_list)
        for i, d in enumerate(list_data):
            d["vector"] = embeddings[i]
        
        MilvusStorge.create_partition_if_not_exist(
            self._client, 
            self.namespace, 
            self.partition_name
        )
        #使用自定义分区名存储新入库的数据
        results = await self.loop.run_in_executor(None,self._client.upsert,self.namespace,list_data,None,self.partition_name)
        logger.info(f"Upsert operation completed successfully in partition {self.partition_name}")
        return results 

async def graph_inser(uid,kb_name,textsliter_name,max_token_size,chunk_overlap,api_key,llm_model,emb_model,base_url,text,separator):
    kb_path = pa(WORKING_DIR) / str(uid) / kb_name
    custom_chunk_func = await select_textspilter(textsliter_name,max_token_size,chunk_overlap,separator)
    llm_func =await get_llm_func(api_key=api_key, model= llm_model, base_url=base_url + "/v1")
    emb_func =await get_emb_func(api_key=api_key, base_url=base_url + "/v1",model=emb_model)
    rag = GraphRAG(
        working_dir=kb_path,
        best_model_func=llm_func,
        cheap_model_func=llm_func,
        embedding_func=emb_func,
        embedding_func_max_async=3,
        embedding_batch_num = 320,
        chunk_func=custom_chunk_func,
        enable_llm_cache=True,
        vector_db_storage_cls=MilvusStorge
    )
    await rag.ainsert(text)
    return {"msg":"success"}
if __name__ == '__main__':
    x = get_content_type("cc092401cb99450293fd5f32ae54af5b.webp")