import abc

import pandas as pd
from src.exceptions import *
from models.proxy import *
from utils.functions import testProxy, ip_verify, area_verify


class SearchProxy:

    def __init__(self, uid=None):
        self.uid = uid

    def byTokenID(self, token_id):
        token_obj = get_user_token_by_id(token_id)
        if not token_obj or not self.__conditionOneToken(token_obj):
            raise NoThisTokenError()
        return token_obj

    def tokens(self, after_ts:int=None, before_ts:int=None, status:str=""):
        if self.uid:
            return get_user_token(self.uid, self.staticProxyOrNot(), after_ts, before_ts, status)

    def byTokenUsername(self, username, password):
        token_obj = get_user_token_by_username_and_password(username, password)
        if not token_obj or not self.__conditionOneToken(token_obj):
            raise NoThisTokenError()
        return token_obj

    def __conditionOneToken(self, token_obj) -> bool:
        return ((self.staticProxyOrNot() is not None and (self.staticProxyOrNot() == bool(token_obj['is_static'])))
                and (self.uid and self.uid == token_obj['uid']))  # 此token属于此用户

    @abc.abstractmethod
    def staticProxyOrNot(self):
        """ 子类继承这个类用于，并直接返回bool类型数值 返回True说明查找静态代理，返回False说明查找动态代理，返回None说明不区分"""
        return None


class SearchDynamicProxy(SearchProxy):

    def staticProxyOrNot(self):
        return False


class SearchStaticProxy(SearchProxy):

    def staticProxyOrNot(self):
        return True

    def display(self, limit:int=1, offset:int=10) -> dict:
        using_pi_ids = list(set([token['pi_id'] for token in get_user_using_static_proxies(self.uid)]))
        # log.info((uid, user_using_proxies_pi_ids))
        total, proxies = search_static_proxies(offset, limit, without_pi_ids=using_pi_ids)
        proxies = [self.parse_proxy(proxy) for proxy in proxies]
        machines = {machine['id']: {
            'ip': str(ipaddress.ip_address(machine['ip_addr'])),
            'supplier': machine['suppliers'] if machine['suppliers'] != "aliyun" else "CN_Proxy",
            'only_http': machine['only_http']
        } for machine in get_all_machines()}
        # log.info(proxies)
        return {'proxies': proxies, 'total': total, 'machines': machines}

    @staticmethod
    def parse_proxy(item):
        item['id'], item['pm_id'] = int(item['id']), int(item['pm_id'])
        item['ip_addr'] = item['ip_addr'].split(".")[0] + ".*.*.*"  # if ip_addr == "" or len(ip_addr.split("*")) == 1 else ip_addr
        return item

    def _chooseOne(self, proxies_data):
        proxies_df = pd.DataFrame(proxies_data)
        if proxies_df.empty:  # 查不到匹配
            return {'proxies': [], 'total': 0, 'machines': {}}
        machines = {machine['id']: {
            'id': machine['id'],
            'ip': str(ipaddress.ip_address(machine['ip_addr'])),
            'supplier': machine['suppliers'] if machine['suppliers'] != "aliyun" else "CN_Proxy",
            'only_http': machine['only_http']
        } for machine in get_all_machines()}
        using_pi_ids = tuple(set([token['pi_id'] for token in get_user_using_static_proxies(self.uid)])) if self.uid else ()
        print(using_pi_ids)
        not_using_proxies = proxies_df[-proxies_df['id'].isin(using_pi_ids)]
        if not not_using_proxies.empty:   # 如果符合搜索条件的静态代理中 还有没被该用户使用的代理
            setting = get_sell_setting_by_type("static_proxy")
            static_proxy_how_many = setting['how_many']         # 加载静态代理多卖配置
            not_using_proxies = not_using_proxies.sample(frac=1)  # 打乱
            for idx in range(0, not_using_proxies.shape[0]):
                res_proxy = not_using_proxies.iloc[idx].to_dict()  # 按打乱之后的顺序 验证代理是否可用
                res_proxy = self.parse_proxy(res_proxy)
                if len(get_proxy_using_unexpired_condition(res_proxy['id'])) >= static_proxy_how_many:   # 代理多买数量是否超过阈值
                    continue
                # 验证可用性
                proxy_obj = get_proxy_ip(res_proxy['id'])
                _machine = machines[res_proxy['pm_id']]
                _proxy = f"{'http' if _machine['only_http'] else 'socks5'}://"
                if "username" in proxy_obj and proxy_obj['username'] != "":
                    _proxy += f"{proxy_obj['username']}:{proxy_obj['password']}@"
                _proxy += f"{_machine['ip']}:{proxy_obj['forward_port']}"
                if testProxy(_proxy):  # 测试代理当前是否可用
                # if True:  # 调试的时候使用
                    print(res_proxy)
                    return {'proxies': [res_proxy], 'total': 1, 'machines': {_machine['id']: _machine}}
                change_proxy_ips_health(res_proxy['id'], health=0)
        using_proxies = proxies_df[proxies_df['id'].isin(using_pi_ids)]
        if not using_proxies.empty:  # 用户使用的代理中 有 匹配的道代理
            res_proxy = proxies_df.sample(1).iloc[0].to_dict()   # 随机取一个 并标记为用户已使用
            res_proxy = self.parse_proxy(res_proxy)
            res_proxy['using'] = True
            print(res_proxy)
            _machine = machines[res_proxy['pm_id']]
            return {'proxies': [res_proxy], 'total': 1, 'machines': {_machine['id']: _machine}}
        return {'proxies': [], 'total': 0, 'machines': {}}

    def byIP(self, ip_addr, network='http'):
        is_socks = not network == 'http'
        ip_addr = ip_verify(ip_addr)
        proxies_data = get_static_proxies_by_ip(str(ip_addr), is_socks=is_socks)
        return self._chooseOne(proxies_data)

    def byArea(self, country=None, state=None, city=None, network='http'):
        is_socks = not network == 'http'
        country, state, city = area_verify(country, state, city)
        proxies_data = get_static_proxies_by_area(country[0], state[0], city[0], is_socks=is_socks)
        return self._chooseOne(proxies_data)

