import db
from utils.decorators import cache


@cache(3600)
def get_country_list() -> list:
    return db.mysql.query("SELECT id, name, code FROM t_geo_country WHERE deleted_on = 0 order by name")


@cache(3600)
def get_country_by_code(country_code: str) -> dict:
    return db.mysql.get("SELECT id, name, code FROM t_geo_country WHERE deleted_on = 0 AND code = %s", country_code)


@cache(3600)
def get_country_by_id(id: int) -> dict:
    return db.mysql.get("SELECT id, name, code FROM t_geo_country WHERE deleted_on = 0 AND id = %s", id)


# 判断用户传入的地区在不在特殊区域白名单，在的话，传回特殊区域的类型和la_ids
def get_country_status(id: int, uid: int) ->list:
    return db.mysql.query("SELECT la_ids from  t_proxy_special_area where area_id= %s and area_type=0 and special_type = %s", id,uid)


def get_city_status(id: int, uid: int) ->list:
    return db.mysql.query("SELECT la_ids from  t_proxy_special_area where area_id= %s and area_type=1 and special_type = %s", id,uid)


def get_state_by_area(country_id: int, city_id: int) -> dict:
    return db.mysql.get("SELECT id FROM t_proxy_special_area WHERE  (area_id = %s and area_type=0) or (area_id = %s and area_type=1) ", country_id, city_id)

@cache(1800)
def get_state_list(country_id: int) ->list:
    return db.mysql.query("SELECT id, country_id, name, code FROM t_geo_state WHERE country_id = %s AND deleted_on = 0 ORDER BY name", country_id)


@cache(1800)
def get_state_by_code(country_id: int, state_code: str) -> dict:
    return db.mysql.get("SELECT id, country_id, name, code FROM t_geo_state WHERE deleted_on = 0 AND country_id = %s AND code = %s", country_id, state_code)


@cache(1800)
def get_state_by_name(country_id: int, state_name: str) -> dict:
    return db.mysql.get("SELECT id, country_id, name, code FROM t_geo_state WHERE deleted_on = 0 AND country_id = %s AND name = %s", country_id, state_name)


@cache(1800)
def get_state_by_id(state_id: int) -> dict:
    return db.mysql.get("SELECT id, country_id, name, code FROM t_geo_state WHERE deleted_on = 0 AND id = %s", state_id)


@cache(1800)
def get_city_list(state_id: int) -> list:
    return db.mysql.query("SELECT id, state_id, name FROM t_geo_city WHERE state_id = %s AND deleted_on = 0 ORDER BY name", state_id)


@cache(1800)
def get_city_by_name(state_id: int, city_name: str) -> dict:
    return db.mysql.get("""
        SELECT id, state_id, name FROM t_geo_city 
        WHERE state_id = %s AND name = %s AND deleted_on = 0 LIMIT 1
    """, state_id, city_name)


@cache(1800)
def get_city_by_id(city_id: int) -> dict:
    return db.mysql.get("SELECT id, state_id, name FROM t_geo_city WHERE id = %s AND deleted_on = 0", city_id )
