import abc
from typing import List, Union
import json
import numpy as np
import random
import requests
import constants

from libs import torndb
from models.proxy import *
from src.base import MakeProxyBase, ByProxyID, ByArea
from src.exceptions import *
from utils.functions import randomString
from log import get_logger

log = get_logger(__name__)


class BuyLumTrafficProxy(MakeProxyBase, ByArea):
    allow_auto_country, is_data_center = False, False

    def __init__(self,*args,**kwargs):
        self.special_id_list = None
        self.supplier_id = 0
        self.network_type = kwargs.get("network_type","http")
        self.s = kwargs.get("s")


    def _testLuminatiProxy(self, username: str, password: str, port: int = 22225,
                           domain: str = "brd.superproxy.io", test_url: str = "http://lumtest.com/myip.json",
                           extract_ip_fun=lambda x: json.loads(x)['ip']) -> Union[str, None]:
        """
           测试 Luminati 代理

           Args:
               username (str): Luminati 代理的用户名
               password (str): Luminati 代理的密码
               port (int): Luminati 代理的端口号，默认为 22225
               domain (str): Luminati 代理的域名，默认为 "brd.superproxy.io"
               test_url (str): 测试代理的 URL，默认为 "http://lumtest.com/myip.json"
               extract_ip_fun : 解析响应中 IP 的函数，默认为 None，表示直接返回响应中的 IP 字符串

           Returns:
               Optional[str]: 测试成功时返回解析后的 IP 地址，测试失败时返回 None

           """

        try:
            proxy = f"http://{username}:{password}@{domain}:{port}"
            log.info(f"Testing Luminati proxy: {proxy}, username: {username}, password: {password}")
            res = requests.get(url=test_url, proxies={'http': proxy, 'https': proxy}, timeout=20)
            res.raise_for_status()
            log.info(f"Luminati proxy test result: {res.text}")
            ip_address = extract_ip_fun(res.text) if extract_ip_fun else ""
            log.info(f"解析出的 IP 地址：{ip_address}")
            return ip_address
        except Exception as e:
            resp_text = res.text if 'resp' in locals() else ''
            log.warning(f"Failed to test Luminati proxy: {e} Response:{resp_text}")
            return None

    def chooseLuminatiObj(self, luminati_objs: List[dict]) -> dict:
        """
        chooseLuminatiObj 在luminati账号中抽选一个账号， 默认随机抽取一个。 可继承后修改选取的方法。

        :param List[dict] luminati_objs: luminati账号对象列表
        :return dict: 选取的luminati账号对象
        """
        return random.choice(luminati_objs) if luminati_objs else None

    def choose(self) -> int:
        luminati_objs = get_luminati_accounts(
            only_country=not self.state[0] and not self.city[0],
            is_static=self.is_static,
            is_data_center=self.is_data_center,
            ids=self.special_id_list
        )
        luminati_obj = self.chooseLuminatiObj(luminati_objs)
        if not luminati_obj:
            self.set_err(constants.ERR_MACHINE_DEFICIENCY)
            return 0
        pm_id = luminati_obj['pm_id']
        if pm_id:  # 指定了使用哪个luminati host
            machine_obj = get_machine_by_id(pm_id)
            if not machine_obj:
                self.set_err(constants.ERR_MACHINE_DEFICIENCY)
                return 0
        else:  # 没有指定 选取第一个
            machine_objs = get_machines_by_supplier("luminati")
            if not machine_objs:
                self.set_err(constants.ERR_MACHINE_DEFICIENCY)
                return 0
            machine_obj = machine_objs[0]
        self.supplier_id = machine_obj['ps_id']
        token = ((f"-country-{self.country[1].lower()}" if self.country[1] else "")
                 + (f"-state-{self.state[1].lower()}" if self.state[1] else "")
                 + (f"-city-{self.city[1].replace(' ', '_').lower()}" if self.city[1] else ""))

        forward_port = machine_obj['forward_port_start'] or machine_obj['forward_port_end']
        wrapped_result = self.tokenWrapper(uid=self.uid, username=luminati_obj['username'], token=token,
                                           password=luminati_obj['password'], port=forward_port,
                                           domain=machine_obj['domain'])
        if not wrapped_result:
            self.set_err(constants.ERR_NEW_PROXY_FAILED)
            return 0
        self._la_id = luminati_obj['id']
        db_session = torndb.Connection(**constants.mysql_config)
        socks5_port = machine_obj['socks5_port']
        if self.network_type == "socks5" :
            forward_port = socks5_port
        return add_proxy_ips(ip_id=wrapped_result[1], pm_id=machine_obj['id'], forward_port=forward_port,
                             country_id=self.country[0], state_id=self.state[0], city_id=self.city[0],
                             online=True, status=True, health=True, username=wrapped_result[0],
                             password=luminati_obj['password'], db_session=db_session)

    @abc.abstractmethod
    def tokenWrapper(self, uid: int, username: str, token: str, password: str, port: int = 22225,
                     domain: str = "brd.superproxy.io") -> (str, int):
        """
        实现该函数用于测试token并封装好最终可用的token
        :param uid:         用户ID
        :param username:    luminati账号用户名
        :param token:       原始token 只包含 luminati账号、通道、地区参数
        :param password:    luminati账号的密码
        :param port:        代理端口
        :param domain:      代理域名
        :return:   :type: (str, int)    token可用时返回最终的token, 和返回存放好数据库中的ip id， 不需要ip 这里返回0，不可用时返回 () or None
        """


class BuyDynamicLumTrafficProxy(BuyLumTrafficProxy):
    is_static, allow_auto_country = False, False

    def tokenWrapper(self, uid: int, username: str, token: str, password: str, port: int = 22225,
                     domain: str = "brd.superproxy.io"):
        wrapped_token = username + token + "-session-" + randomString(6, uppercase=False)
        # 测试代理是否可用
        return (wrapped_token, 0)


class BuyStaticLumTrafficProxy(BuyLumTrafficProxy):
    is_static, allow_auto_country = True, False

    def choose(self) -> int:
        # if self.uid not in (18388,10042,5411):
        #     return super().choose()
        country_id = self.country[0]
        state_id = 0
        city_id = 0
        if self.state:
            state_id = self.state[0]
        if self.city:
            city_id = self.city[0]

        # 随机获取一个用户不使用的ip
        res = get_t_proxys(country_id, self.uid, self.is_data_center, state_id=state_id, city_id=city_id,network_type=self.network_type,s=self.s)
        # 随机获取一个用户不使用的ip
        if not res:
            return 0
        record, ip_id = res




        id = add_proxy_ips(record["pm_id"], record["forward_port"],0,ip_id,record['country_id'],0,0,True,True,True, record["username"], record["password"]
                      )
        self._la_id = record["la_id"]
        if id >0:
            # 绑定关系
            insert_traffic_ip(record["ti_id"],id,self.uid)
        return id


    def chooseLuminatiObj(self, luminati_objs: List[dict]) -> dict:
        if not luminati_objs:
            return None
        elif len(luminati_objs) == 1:
            return luminati_objs[0]

        luminati_objs_map = {obj['id']: obj for obj in luminati_objs}
        if self.uid == 5705:
            return luminati_objs_map.get(random.randint(15,16))
        laid_ratio = get_user_laid_ratio(self.uid, tuple(luminati_objs_map.keys()))
        if len(laid_ratio) <= 0:
            return super().chooseLuminatiObj(luminati_objs)
        _laid_ratio = {i['la_id']: i['num'] for i in laid_ratio}
        laid_ratio = {i['id']: 1 for i in luminati_objs}
        laid_ratio.update(_laid_ratio)
        p = np.array(tuple(laid_ratio.values()), dtype=np.float)
        p /= p.sum()
        tag_id = int(np.random.choice(tuple(laid_ratio.keys()), size=1, p=p)[0])
        return luminati_objs_map.get(tag_id)

    def tokenWrapper(self, uid: int, username: str, token: str, password: str, port: int = 22225,
                     domain: str = "brd.superproxy.io", ):
        # 测试代理是否可用
        ip_addr, ip_id = None, 0
        for _ in range(6):  # 出现用户已经使用过的代理的尝试次数，降低同一个用户申请到同一个IP的几率，尝试N次后还是重复那就使用最后一次的
            # 测试代理是否可用
            _ip_addr = self._testLuminatiProxy(username=username + token, password=password, port=port, domain=domain,
                                               # test_url=f"https://ipinfo.io/json?token={constants.IP_INFO_TOKEN}"
                                               )
            if not _ip_addr:
                continue
            ip_id = add_ip_address(_ip_addr)
            if not is_ip_id_in_user(uid, ip_id):
                ip_addr = _ip_addr
                break
        return (username + "-ip-" + ip_addr, ip_id) if ip_addr and ip_id else ()


class BuyDataCenterStaticLumTrafficProxy(BuyLumTrafficProxy):
    is_static, allow_auto_country, is_data_center = True, False, True

    def choose(self) -> int:
        # if self.uid not in (18388, 10042):
        #     return super().choose()
        country_id = self.country[0]
        state_id = 0
        city_id = 0
        if self.state:
            state_id = self.state[0]
        if self.city:
            city_id = self.city[0]

        # 随机获取一个用户不使用的ip
        res = get_t_proxys(country_id, self.uid, self.is_data_center, state_id=state_id, city_id=city_id,network_type=self.network_type)
        if not res:
            return 0
        record, ip_id = res

        id = add_proxy_ips(record["pm_id"], record["forward_port"], 0, ip_id, record['country_id'], 0, 0, True, True,
                           True, record["username"], record["password"]
                           )
        self._la_id = record["la_id"]
        if id > 0:
            # 绑定关系
            insert_traffic_ip(record["ti_id"], id, self.uid)
        return id

    def chooseLuminatiObj(self, luminati_objs: List[dict]) -> dict:
        if not luminati_objs:
            return None
        elif len(luminati_objs) == 1:
            return luminati_objs[0]
        luminati_objs_map = {obj['id']: obj for obj in luminati_objs}
        laid_ratio = get_user_laid_ratio(self.uid, tuple(luminati_objs_map.keys()))
        if len(laid_ratio) <= 0:
            return super().chooseLuminatiObj(luminati_objs)
        _laid_ratio = {i['la_id']: i['num'] for i in laid_ratio}
        laid_ratio = {i['id']: 1 for i in luminati_objs}
        laid_ratio.update(_laid_ratio)
        p = np.array(tuple(laid_ratio.values()), dtype=np.float)
        p /= p.sum()
        tag_id = int(np.random.choice(tuple(laid_ratio.keys()), size=1, p=p)[0])
        return luminati_objs_map.get(tag_id)

    def tokenWrapper(self, uid: int, username: str, token: str, password: str, port: int = 22225,
                     domain: str = "brd.superproxy.io", ):
        # 测试代理是否可用
        ip_addr, ip_id = None, 0
        for _ in range(6):  # 出现用户已经使用过的代理的尝试次数，降低同一个用户申请到同一个IP的几率，尝试N次后还是重复那就使用最后一次的
            # 测试代理是否可用
            _ip_addr = self._testLuminatiProxy(username=username + token, password=password, port=port, domain=domain,
                                               # test_url=f"https://ipinfo.io/json?token={constants.IP_INFO_TOKEN}"
                                               )
            if not _ip_addr:
                continue
            ip_id = add_ip_address(_ip_addr)
            if not is_ip_id_in_user(uid, ip_id):
                ip_addr = _ip_addr
                break
        return (username + "-ip-" + ip_addr, ip_id) if ip_addr and ip_id else ()


class BuyDbIpTrafficProxy(BuyLumTrafficProxy):
    """
    处理在库的ip，不需要申请
    返回ip，让流程完整进行，但从数据库中获取ip，而不是去申请
    """

    def chooseLuminatiObj(self, luminati_objs: List[dict]) -> dict:
        pass

    def tokenWrapper(self, uid: int, username: str, token: str, password: str, port: int = 22225,
                     domain: str = "brd.superproxy.io") -> (str, int):
        """
        返回ip，让流程完整进行，但从数据库中获取ip，而不是去申请
        """
        pass


def extractIPAddress(resp_txt) -> str:
    ip_obj = json.loads(resp_txt)
    ip_address = ip_obj.get('data', {}).get('ip', "")
    return ip_address