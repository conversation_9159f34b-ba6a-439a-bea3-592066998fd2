from typing import Union

import ipaddress

import time
import traceback
import db
from log import get_logger
from utils.decorators import timer

log = get_logger(__name__)


def set_machine_fault(machine_id, fault) -> bool:
    """
    设置机器是否故障
    :param machine_id:
    :param fault:
    :return:
    """
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_proxy_machines SET modified_on = %s, fault = %s WHERE deleted_on = 0 AND id = %s
        """, t, fault, machine_id)
        return effect > 0
    except:
        traceback.print_exc()
        return False


def get_all_machines() -> list:
    """
    获取所有的机器
    :return:
    """
    return db.mysql.query("""
        SELECT t_proxy_machines.id, ip_id, CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, domain, 
        is_linux, forward_port_start, forward_port_end, 
        t_proxy_suppliers.name as suppliers, only_http, self_check, fault  FROM t_proxy_machines 
        LEFT JOIN t_proxy_suppliers ON t_proxy_machines.ps_id = t_proxy_suppliers.id 
        LEFT JOIN t_ips ON t_proxy_machines.ip_id = t_ips.id 
        WHERE t_proxy_machines.deleted_on = 0 AND t_proxy_suppliers.deleted_on = 0 
    """)


def get_machine_by_id(pm_id:int) -> dict:
    """
    通过ID获取机器
    :param pm_id:
    :return:
    """
    return db.mysql.get("""
        SELECT t_proxy_machines.id AS id, CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, ps_id,
        forward_port_start, forward_port_end FROM t_proxy_machines
        LEFT JOIN t_ips ON t_proxy_machines.ip_id = t_ips.id
        WHERE t_proxy_machines.id = %s AND t_proxy_machines.deleted_on = 0
    """, pm_id)


def get_supplier_by_id(supplier_id:int) -> dict:
    """
    通过supplier ID 获取supplier
    :param supplier_id:
    :return:
    """
    return db.mysql.get(""" SELECT * FROM t_proxy_suppliers WHERE id = %s AND deleted_on = 0 """, supplier_id)


def get_online_machine_by_supplier(supplier:str) -> list:
    """
    根据supplier查找在线的机器
    :param supplier:   t_proxy_suppliers.name
    :return:
    """
    return db.mysql.query("""
        SELECT t_proxy_machines.id as id, ip_id, t_proxy_suppliers.name, is_linux, 
        forward_port_start, forward_port_end, only_http, self_check FROM t_proxy_machines 
        LEFT JOIN t_proxy_suppliers ON t_proxy_machines.ps_id = t_proxy_suppliers.id 
        WHERE t_proxy_machines.deleted_on = 0 AND t_proxy_suppliers.deleted_on = 0 
        AND t_proxy_suppliers.name = %s
    """, supplier)


def get_machines_by_supplier(supplier:str) -> list:
    """
    得到某个供应商的所有机器
    :param supplier:    供应商名 t_proxy_suppliers.name
    :return:
    """
    return db.mysql.query("""
        SELECT t_proxy_machines.id, CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, 
        is_linux, forward_port_start, forward_port_end, 
        t_proxy_suppliers.name as suppliers, only_http, self_check, fault  FROM t_proxy_machines 
        LEFT JOIN t_proxy_suppliers ON t_proxy_machines.ps_id = t_proxy_suppliers.id 
        LEFT JOIN t_ips ON t_proxy_machines.ip_id = t_ips.id 
        WHERE t_proxy_machines.deleted_on = 0 AND t_proxy_suppliers.deleted_on = 0 AND t_proxy_suppliers.name = %s
    """, supplier)


def get_machines_condition(machine_ids: list) -> list:
    """
    获取机器的使用情况，已经使用了多少端口（并根据多到少排序）
    :param machine_ids:     机器ID的列表
    :return:  查询到的情况 返回 {机器ID: 已使用端口数}的列表   eg: [{1: 3}, ...]
    """
    return db.mysql.query("""
        SELECT pm_id, count(pm_id) FROM t_proxy_ips 
        WHERE online = 1 AND status = 1 AND deleted_on = 0 AND pm_id IN %s
        GROUP BY pm_id ORDER BY count(pm_id) DESC 
    """, machine_ids )


def get_one_machine_condition(pm_id:list) -> dict:
    """
    获取一个机器的端口使用情况
    :param pm_id:
    :return:
    """
    return db.mysql.get("SELECT pm_id, COUNT(1) AS total FROM t_proxy_ips WHERE deleted_on = 0 AND online = 1 AND status = 1 AND pm_id = %s", pm_id)


def     get_machine_using_forward_port(machine_id: int) -> list:
    """
    获取每个机器当前被使用的端口
    :param machine_id:  机器的ID
    :return: 返回端口整形列表  eg: [4001, 4003, ...]
    """
    # 满足正在使用的端口的条件： 1 未过期（online = 1）；  2 当初请求开端口时返回成功（status = 1）
    using_port_dicts = db.mysql.query("SELECT distinct forward_port FROM t_proxy_ips WHERE deleted_on = 0 AND online = 1 AND status = 1 AND pm_id = %s ", machine_id)
    return [using_port_dict['forward_port'] for using_port_dict in using_port_dicts]


def add_proxy_ips(pm_id: int=0, forward_port: int=0, port: int=0, ip_id:int=0, country_id: int=0, state_id: int=0,
                  city_id: int=0, online: bool=False, status: bool=False, health: bool=False) -> int:
    """
    插入一条数据到t_proxy_ips
    :param pm_id:
    :param forward_port:
    :param port:
    :param country_id:
    :param state_id:
    :param city_id:
    :param online:
    :param status:
    :param health:
    :return:  成功：插入记录的t_proxy_ips对应的ID   失败：-1
    """
    t = int(time.time())
    try:
        return db.mysql.execute("""
        INSERT INTO t_proxy_ips (created_on, modified_on, deleted_on, pm_id, forward_port, ip_id, port,
        country_id, state_id, city_id, online, status, health)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, t, t, 0, pm_id, forward_port, ip_id, port, country_id, state_id, city_id, online, status, health)
    except:
        log.error(traceback.format_exc())
        return -1


def change_proxy_ips_status(p_ip_id: int, status: bool) -> bool:
    """
    更新代理的状态，即请求机器是否成功返回代理
    :param p_ip_id: t_proxy_ips的ID
    :param status:  True：成功 / False：失败
    :return:    执行sql是否成功
    """
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_proxy_ips SET modified_on = %s, status = %s WHERE id = %s AND deleted_on = 0
        """, t, status, p_ip_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def change_proxy_ip_area(p_ip_id:int, country_id=0, state_id=0, city_id=0) -> bool:
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_proxy_ips SET modified_on = %s, country_id = %s, state_id = %s, city_id = %s 
            WHERE id = %s AND deleted_on = 0
        """, t, country_id, state_id, city_id, p_ip_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def change_proxy_ips_machine_and_port(p_ip_id: int, pm_id:int, port, forward_port) -> bool:
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("UPDATE t_proxy_ips SET modified_on = %s, pm_id = %s, port = %s, forward_port = %s "
                                           "WHERE deleted_on = 0 AND id = %s ", t, pm_id, port, forward_port, p_ip_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def insert_ip_id(proxy_ip_id:int, ip_addr:Union[int, str]) -> bool:
    t = int(time.time())
    try:
        ip_addr = ipaddress.ip_address(ip_addr)
        ip_addr_in_db = db.mysql.get(""" 
            SELECT * FROM t_ips WHERE ip_addr = %s AND deleted_on = 0 
        """, int(ip_addr))
        if ip_addr_in_db:
            ip_id = ip_addr_in_db['id']
        else:
            ip_id = db.mysql.execute("""
                INSERT INTO t_ips (ip_addr, ip_address, created_on, modified_on, deleted_on ) 
                VALUES (%s, HEX(INET6_ATON(%s)), %s, %s, %s)
            """, int(ip_addr) if type(ip_addr) == ipaddress.IPv4Address else 0 ,str(ip_addr), t, t, 0)
        db.mysql.execute("""
            UPDATE t_proxy_ips SET modified_on = %s, ip_id = %s, online = 1, health = 1 WHERE id = %s AND deleted_on = 0 
        """, t, ip_id, proxy_ip_id)
        return True
    except:
        log.error(traceback.print_exc())
        return False


def get_same_ip(ip_addr:int) -> dict:
    return db.mysql.get("""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_address, 
        t_proxy_suppliers.name AS suppliers FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id 
        LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id 
        WHERE ip_addr = %s AND t_proxy_ips.deleted_on = 0 
        AND online = 1 AND status = 1 AND health = 1 
        AND t_proxy_suppliers.name = '911' ORDER BY t_proxy_ips.created_on DESC LIMIT 1
    """, ip_addr)