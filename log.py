import logging
import os
from logging.handlers import RotatingFileHandler


def get_logger(name: str, log_file: str = "log.txt", max_bytes: int = 1024 * 1024, backup_count: int = 5) -> logging.Logger:
    """
    获取一个logger对象，并设置其日志输出格式、级别和handler
    :param name: logger的名字，通常是模块名或者 __name__，用于区分不同的logger
    :param log_file: 日志文件名，默认为log.txt
    :param max_bytes: 每个日志文件的最大字节数，默认为1MB
    :param backup_count: 保留的日志文件个数，默认为5
    :return: logger对象
    """

    log_folder = "log"
    if not os.path.exists(log_folder):
        os.mkdir(log_folder)

    log_file = os.path.join(log_folder, log_file)
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)  # 设置日志级别为 INFO

    # 设置日志输出格式
    formatter = logging.Formatter(
        "[%(asctime)s] %(levelname)s [%(pathname)s:%(lineno)d] %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )

    # 输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 输出到文件，滚动保存日志文件
    file_handler = RotatingFileHandler(log_file, maxBytes=max_bytes, backupCount=backup_count)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger


if __name__ == "__main__":
    log = get_logger(__name__)
    log.info("abc")
