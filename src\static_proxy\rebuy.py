from src.exceptions import *
from src.base import MakeProxyBase, ByUserTokenID
from models.proxy import *
from utils.functions import testProxy


class Rebuy(MakeProxyBase, ByUserTokenID):

    def __init__(self):
        self.setting = get_sell_setting("static_proxy")
        self.is_static = True

    def _check_proxy(self, machine, proxy_obj, is_socks=False) -> bool:
        _proxy = f"{'socks5' if is_socks else 'http'}://"
        if 'username' in proxy_obj and proxy_obj['username']:
            _proxy += f"{proxy_obj['username']}:{proxy_obj['password']}@"
        _proxy += f"{str(ipaddress.ip_address(machine['ip_addr']))}:{proxy_obj['forward_port']}"
        return testProxy(_proxy)

    def choose(self):
        if self.life_time < 1:
            raise TypeError()
        token_obj = get_user_token_by_id(self.user_token_id)
        if not token_obj or not token_obj['is_static'] or token_obj['uid'] != self.uid:  # 不是静态代理或者代理token不属于该用户
            raise NoThisTokenError()
        if not token_obj['expired']:  # 代理token未过期
            raise UserTokenNotExpiredError()
        proxy_obj = get_proxy_ip(token_obj['pi_id'])
        if not (proxy_obj['health'] and proxy_obj['online'] and proxy_obj['status']):  # 如果代理不是处于正常状态， 不允许再次购买
            raise ProxyNotOnlineError()
        # 多卖量检查
        setting = get_sell_setting_by_type("static_proxy")
        if len(get_proxy_using_unexpired_condition(proxy_obj['id'])) >= setting['how_many']:   # 此代理现在被太多疼使用
            raise TooManyUserUsingThisProxyError()
        # 验证可用性
        _machine = get_machine_by_id(proxy_obj['pm_id'])
        _supplier = get_supplier_by_id(_machine['ps_id'])
        if not self._check_proxy(_machine, proxy_obj, is_socks= not _supplier['only_http']):    # 测试代理当前是否可用,如果不可用
        # if False:  # 调试的时候使用
            change_proxy_ips_health(proxy_obj['id'], health=0)
            raise ProxyNotOnlineError()
        # 返回proxy_id
        return token_obj['pi_id']

