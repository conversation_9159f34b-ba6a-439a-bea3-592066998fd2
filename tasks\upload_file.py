# -*- coding: utf-8 -*-
# @Time    : 2024/2/27 11:42
# <AUTHOR> hzx1994
# @File    : upload_file.py
# @Software: PyCharm
# @description:
from utils.upload_blob import upload,update_from_url as upload_url,upload_blob_func,graph_inser
from app import app
@app.task
def update_from_url(url,blob_path,name):
    return upload_url(url,blob_path,name)

@app.task
def upload_file(file,blob_path="imgs"):
    # 实现文件上传的逻辑
    # ...
    return upload(file,blob_path)

@app.task
def upload_blob(data = None, file_name=None, pre='imgs',proxy_blob=False,content_type=None):
    import base64
    decoded_string = base64.b64decode(data)
    return upload_blob_func(data=decoded_string,file_name=file_name,pre=pre,proxy_blob=proxy_blob,content_type=content_type)

@app.task()
def upload_graph(uid,kb_name,textsliter_name,max_token_size,chunk_overlap,api_key,llm_model,emb_model,base_url,text,separator=None):
    import asyncio
    loop = asyncio.get_event_loop()
    result = loop.run_until_complete(graph_inser(uid,kb_name,textsliter_name,max_token_size,chunk_overlap,api_key,llm_model,emb_model,base_url,text,separator))
    return result