import abc
from src.base import Make<PERSON>roxyB<PERSON>, ByIP, ByArea
from models.proxy import *
import pandas as pd

from utils.functions import probability


class Sec911(MakeProxyBase):
    def __init__(self):
        self.setting = get_sell_setting("free_911")

    @abc.abstractmethod
    def search(self) -> pd.DataFrame:
        pass

    def withoutUserUsingProxy(self, proxy_df:pd.DataFrame) -> pd.DataFrame:
        if not self.uid or proxy_df.empty:
            return proxy_df
        user_using_proxies_id = tuple(set([token['pi_id'] for token in get_user_using_proxy(self.uid)]))
        return proxy_df[-proxy_df['id'].isin(user_using_proxies_id)]

    def choose(self) -> int:
        if not self.setting['on_off']:
            return 0
        prob = float(self.setting['probability'])  # 给用free911的概率
        if not probability(prob):
            return 0
        sec911_proxy_df = self.withoutUserUsingProxy(self.search())
        if sec911_proxy_df.empty:
            return 0
        sec911_proxy_df['using_count'] = sec911_proxy_df['id'].apply(lambda ip_id: len(get_proxy_using_condition(ip_id)))
        sec911_proxy_df = sec911_proxy_df[sec911_proxy_df['using_count'] < self.setting['how_many']]
        if sec911_proxy_df.empty:
            return 0
        random_one = sec911_proxy_df.sample(1)
        log.info(random_one)
        return int(random_one.iloc[0]['id'])


class Sec911ByIP(Sec911, ByIP):
    def search(self) -> pd.DataFrame:
        if self.fuzzy_query:
            return pd.DataFrame(get_sec911_proxy_by_ip(self.ip_addr))
        return pd.DataFrame(get_sec911_proxy_by_ip(str(self.ip_addr)))


class Sec911ByArea(Sec911, ByArea):
    def search(self) -> pd.DataFrame:
        return pd.DataFrame(get_sec911_proxy_by_area(self.country[0], self.state[0], self.city[0], uid=self.uid))