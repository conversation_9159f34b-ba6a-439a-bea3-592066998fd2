import constants


def result_wrapper(func):
    def inner(*args, **kwargs):
        try:
            result_data = func(*args, **kwargs)
            return {'code': 0, 'msg': "succeed", 'data': result_data or {}}
        except Exception as e:
            if len(e.args) > 0 and isinstance(e, tuple):
                pass
            err = constants.ERR_INTERNAL_ERROR
            return {'code': err[0], 'msg': "", 'data': {}}



### 测试 start ###

class TestError(Exception):
    """ 测试用 """

### 测试 end ###

### 用户相关 start ###

class UserError(Exception):
    """用户ID不存在"""

### 用户相关 end ###

### 参数类型错误 start ###

class NetworkTypeError(TypeError):
    """参数Network类型错误"""


class IPAddressTypeError(TypeError):
    """参数IP地址类型错误"""


class CountryTypeError(TypeError):
    """参数国家类型错误"""


class StateTypeError(TypeError):
    """参数州/市类型错误"""


class CityTypeError(TypeError):
    """参数城市类型错误"""

### 参数类型错误 end ###

### 代理机器相关 start ###

class MachinePortInsufficientError(Exception):
    """机器端口不足"""

class MachineSupplierError(Exception):
    """机器供应商类型不正确"""

class NoMachineError(Exception):
    """没有此机器"""

### 代理机器相关 end ###

### 代理相关 start ###

class ProxyNotOnlineError(Exception):
    """代理不可用"""


class NoProxyError(Exception):
    """此代理不存在"""


class UserUsingThisProxyError(Exception):
    """该用户正在使用此代理"""


class TooManyUserUsingThisProxyError(Exception):
    """此代理被太多用户使用"""


class ThisProxyNotSupplierSocksError(Exception):
    """此代理不支持SOCKS5"""

### 代理相关 end ###

### 代理Token相关 start ###

class NoThisTokenError(Exception):
    """此Token不存在"""


class UserTokenNotExpiredError(Exception):
    """代理未过期"""


class UserTokenExpiredError(Exception):
    """代理已过期"""


### 代理Token相关 end ###

