import abc

import pandas as pd
from src.base import MakeProxyBase, ByIP, ByArea
from models.proxy import *
from utils.functions import probability


class CNProxy(MakeProxyBase):
    def __init__(self):
        self.setting =  get_sell_setting("cn")
        self.is_cn_proxy = True

    def withoutUserUsingProxy(self, proxy_df:pd.DataFrame) -> pd.DataFrame:
        if not self.uid or proxy_df.empty:
            return proxy_df
        user_using_proxies_id = tuple(set([token['pi_id'] for token in get_user_using_proxy(self.uid)]))
        return proxy_df[-proxy_df['id'].isin(user_using_proxies_id)]

    def choose(self):
        if not self.setting['on_off']:
            return 0
        if self.network != "http":   #CN代理只支持HTTP
            return 0
        prob = float(self.setting['probability'])  # 给用CN的概率
        if not probability(prob):
            return 0
        proxies_df = self.withoutUserUsingProxy(self.search()).sample(frac=1)
        if proxies_df.empty:    # 没有相关的代理
            return 0
        for i, row in proxies_df.iterrows():
            if len(get_proxy_using_condition(row['id'])) <= self.setting['how_many']:
                return row['id']
        return 0

    @abc.abstractmethod
    def search(self) -> pd.DataFrame:
        pass


class CNProxyByIp(CNProxy, ByIP):
    def search(self) -> pd.DataFrame:
        if self.fuzzy_query:
            return pd.DataFrame(get_cn_proxies_by_ip_fuzz(self.ip_addr))
        return pd.DataFrame(get_cn_proxies_by_ip(int(self.ip_addr)))


class CNProxyByArea(CNProxy, ByArea):
    def search(self) -> pd.DataFrame:
        if self.country[1] == 'CN':
            return pd.DataFrame(catch_cn_proxy(country_id=self.country[0], state_id=self.state[0], city_id=self.city[0],uid = self.uid))
        else:
            return pd.DataFrame()
