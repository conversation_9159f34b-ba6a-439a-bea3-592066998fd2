import json
import time
from logging.handlers import RotatingFileHandler

import db
from libs import torndb
from models.area import get_country_status, get_city_status, get_state_by_area
from models.ip_order import get_payway, set_ip_order, change_ip_order_status_by_order_id
from models.user import get_balance, get_user_special_areas
from src.dynamic_proxy.buy_911_proxy import BuyProxyByArea, BuyProxyByIP
from src.dynamic_proxy.sec_911 import Sec911ByArea, Sec911ByIP
from src.dynamic_proxy.super_sell import SuperSellByArea, SuperSellByIP
from src.dynamic_proxy.cn_sell import CNProxyByArea, CNProxyByIp
from src.static_proxy.buy import Buy
from src.static_proxy.rebuy import Rebuy
from src.static_proxy.renew import Renew
from models.proxy import *
from src.exceptions import *
import random

from src.traffic_proxy.buy_luna_proxy import BuyLunaTrafficProxy
from src.traffic_proxy.buy_netnut_proxy import BuyNetnutTrafficProxy
from src.traffic_proxy.buy_smart_proxy import BuySmartTrafficProxy
from utils.decorators import cache
from utils.functions import language<PERSON>he<PERSON>, randomString, get_ip_area
import constants
from src.traffic_proxy.buy_lum_proxy import (
    BuyStaticLumTrafficProxy,
    BuyDynamicLumTrafficProxy,
    BuyDataCenterStaticLumTrafficProxy,
)
from src.traffic_proxy.buy_oxylabs_proxy import BuyOxyTrafficProxy
from src.traffic_proxy.buy_lum_api_proxy import BuyLumAPIProxy, BuyStaticLumAPIProxy, BuyDynamicLumAPIProxy

import log as logger

log = logger.get_logger(__name__)


class MakeProxy:
    db_session = None

    supplier_id = 0

    def __init__(self, uid=None, lang="us"):
        self.db_session = torndb.Connection(**constants.mysql_config)
        self.uid = uid
        self.lang = languageChecker(lang)
        self.username_len, self.password_len = 8, 16
        self.special_id_list = []

    @property
    def username_and_password_length(self):
        return self.username_len, self.password_len

    @username_and_password_length.setter
    def username_and_password_length(self, length):
        if len(length) != 2 or not (type(length[0]) == type(length[1]) == int) or not (0 < length[0] <= length[1]):
            raise TypeError(
                "value's type must a tuple and like (int, int)  ...  (username length > 0, password length > 0) "
            )
        self.username_len, self.password_len = length

    def _createdUserToken(
        self,
        proxy_id: int,
        network="http",
        is_cn_proxy=False,
        is_more_sell=False,
        is_static=False,
        life_time=0,
        la_id=0,
        is_api=False,
        is_data_center=False,
    ) -> int:
        if not proxy_id:
            return 0
        url = random.choices(constants.CN_PROXY_SERVERS_LIST if is_cn_proxy else constants.PROXY_SERVERS_LIST)[0]
        username, passwd = randomString(self.username_len), randomString(self.password_len)
        token_id = add_user_token(
            uid=self.uid or 0,
            url=url,
            username=username,
            passwd=passwd,
            pi_id=proxy_id,
            network="http" if network == "http" else "socket",
            is_more_sell=is_more_sell,
            expired=False,
            is_static=is_static,
            life_time=life_time,
            luminati_account_id=la_id,
            is_api=is_api,
            is_data_center=is_data_center,
            ps_id=self.supplier_id,
        )
        return token_id or 0

    def pay(self, payway_type, num=1) -> int:
        """
        购买代理 扣费
        :return:  -> int   成功返回对应订单的订单号   失败返回0
        """
        payway = get_payway(payway_type)
        if not payway:
            return 0
        value, extra_value = payway["pay_value"] * num, payway["extra_value"] * num
        order_id = set_ip_order(
            uid=self.uid,
            type=payway["type"],
            currency_type="",
            currency=0,
            receive_currency=0,
            is_inner=False,
            payway=payway["payway"],
            pay_order="",
            value=value,
            extra_value=extra_value,
            status=1,
            checksum=True,
            valid=True,
            db_session=self.db_session,
        )
        return order_id

    def refund(self, payway_type, num=1, pay_order_id: int = 0) -> int:
        """
        退款
        :param pay_order_id:    需要退款的支付订单的订单号（有对应的订单会把已对应的订单进行隐藏）
        :return:    -> int      成功返回退款订单的订单号  失败返回0
        """
        refund_payway = get_payway(payway_type)
        if not refund_payway:
            return 0
        value, extra_value = refund_payway["pay_value"] * num, refund_payway["extra_value"] * num
        refund_order_id = set_ip_order(
            uid=self.uid,
            type=refund_payway["type"],
            currency_type="",
            currency=0,
            receive_currency=0,
            is_inner=False,
            payway=refund_payway["payway"],
            pay_order="",
            value=value,
            extra_value=extra_value,
            status=2,
            checksum=True,
            valid=True,
            db_session=self.db_session,
        )
        if pay_order_id:  # 如果有需要隐藏的支付订单
            change_ip_order_status_by_order_id(pay_order_id, status=2)  # 订单状态status 设为2代表生效但需要再前端隐藏
        return refund_order_id

    def returnError(self, err, append_msg="", msg="", data=None, lang="us"):
        lang_idx = {"cn": 1, "us": 2}
        msg = msg or (err[lang_idx.get(lang, 2)] + append_msg)
        return {"code": err[0], "msg": msg, "data": data or {}}

    def returnSucceed(self, data: dict, msg="succeed"):
        return {"code": 0, "msg": msg, "data": data or {}}


class MakeDynamicIPProxy(MakeProxy):
    _use_machine_id = None  # 表示使用的机器编号
    _use_more_sell, _use_free_911, _use_cn, _use_911 = True, True, True, True

    @property
    def use_machine_id(self):
        return self._use_machine_id

    @use_machine_id.setter
    def use_machine_id(self, machine_id: int = None):
        self._use_machine_id = machine_id

    def set_use(self, use_more_sell: bool = None, use_free_911: bool = None, use_cn: bool = None, use_911: bool = None):
        self._use_more_sell = use_free_911 if use_more_sell is not None else self._use_more_sell
        self._use_free_911 = use_free_911 if use_free_911 is not None else self._use_free_911
        self._use_cn = use_cn if use_cn is not None else self._use_cn
        self._use_911 = use_911 if use_911 is not None else self._use_911

    def __use_ways_mapping(self, way_more_sell, way_free_911, way_cn, way_911):
        way_list = []
        if self._use_free_911:
            way_list.append(way_free_911)
        if self._use_cn:
            way_list.append(way_cn)
        if self._use_more_sell:
            way_list.append(way_more_sell)
        if self._use_911:
            way_list.append(way_911)
        return way_list

    def createProxyExceptWrapper(self, e: Exception):
        except_type = type(e)
        log.info(except_type)
        except_dict = {
            MachinePortInsufficientError: constants.ERR_MACHINE_DEFICIENCY,
            NoMachineError: constants.ERR_MACHINE_DEFICIENCY,
            NoProxyError: constants.ERR_NEW_PROXY_FAILED,
            RuntimeError: constants.ERR_TIMEOUT_ERROR,
            ValueError: constants.ERR_ARGUMENT_TYPE,
            TypeError: constants.ERR_ARGUMENT_TYPE,
        }
        err = except_dict.get(except_type)
        if err is None:
            traceback.print_exc()
            err = constants.ERR_INTERNAL_ERROR
            return self.returnError(err, lang=self.lang)
        return self.returnError(err, append_msg=str(e), lang=self.lang)

    def byIP(self, ip_addr, result_same_area=False, network=None) -> dict:
        log.info("Entering byIP function.")
        ways_priority_list = self.__use_ways_mapping(
            way_free_911=Sec911ByIP, way_cn=CNProxyByIp, way_more_sell=SuperSellByIP, way_911=BuyProxyByIP
        )
        res_pi_id, res_user_token_id = 0, 0
        pay_order_id = 0
        if self.uid:
            pay_order_id = self.pay("buy_a_dynamic")
            if 0 >= pay_order_id:
                return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH, lang=self.lang)
        try:
            for Way in ways_priority_list:
                way = Way()
                way.uid = self.uid
                way.lang = self.lang
                way.network = network
                way.setIP(ip_addr)
                log.debug(f"Trying proxy supplier: {way.__class__.__name__}")
                if isinstance(way, BuyProxyByIP):
                    way.use_machine_id = self.use_machine_id
                res_pi_id = way.start()
                if res_pi_id != 0:
                    if hasattr(way, "supplier_id"):
                        self.supplier_id = getattr(way, "supplier_id")
                    res_user_token_id = self._createdUserToken(
                        proxy_id=res_pi_id, network=way.network, is_cn_proxy=way.is_cn_proxy, is_more_sell=way.is_more_sell
                    )
                    break
            if result_same_area and not len(ip_addr.split("*")) > 1 and res_pi_id == 0:
                # 获取IP对应的地区
                area_data = get_ip_area(ip_addr)
                if not area_data:
                    raise NoProxyError()
                self.refund("refund_a_dynamic", pay_order_id=pay_order_id)  # 因为byArea 也会扣一次费 所以这里要先退款
                return self.byArea(country=area_data[0][0], state=area_data[1][0], network=network)
            if res_user_token_id <= 0:
                raise NoProxyError()
        except Exception as e:
            if pay_order_id > 0:
                self.refund("refund_a_dynamic", pay_order_id=pay_order_id)
            log.error(f"[byIP] Error occurred while creating proxy for IP address: {ip_addr}. Error: {e}")
            return self.createProxyExceptWrapper(e)
        if res_user_token_id > 0:
            return self.returnSucceed({"token_id": res_user_token_id})
        else:
            if pay_order_id > 0:
                self.refund("refund_a_dynamic", pay_order_id=pay_order_id)
            log.error(f"[byIP] Failed to create proxy for IP address: {ip_addr}.")
            return self.returnError(constants.ERR_INTERNAL_ERROR, lang=self.lang)
    @timer
    def byArea(self, country=None, state=None, city=None, network=None) -> dict:
        st = time.time()
        ways_priority_list = self.__use_ways_mapping(
            way_free_911=Sec911ByArea, way_cn=CNProxyByArea, way_more_sell=SuperSellByArea, way_911=BuyProxyByArea
        )
        res_pi_id, res_user_token_id = 0, 0
        pay_order_id = 0
        log_id = db.mysql.execute(
            "insert into t_user_logs (created_on,modified_on,uid,deleted_on,log_type_id,msg,ip_id,client_ip) values ("
            "%s,%s,%s,%s,%s,%s,%s,%s)", st, st, self.uid, 0,  101,
            f"country_id:{country},state:{state},city:{city}", 0, "")
        if self.uid:
            pay_order_id = self.pay("buy_a_dynamic")

            if 0 >= pay_order_id:
                log.error(f"[byArea] Failed to create proxy. Balance not enough. uid={self.uid}.")
                return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH, lang=self.lang)
        t2= time.time()
        log.info(f"Function: here1 time: {t2-st}s ")
        try:
            for Way in ways_priority_list:
                way = Way()
                way.lang = self.lang
                way.uid = self.uid
                way.network = network
                way.setArea(country, state, city)
                t3 = time.time()
                log.info(f"Function: {ways_priority_list} here2 time: {t3 - t2}s ")
                if isinstance(way, BuyProxyByArea):
                    way.use_machine_id = self.use_machine_id
                res_pi_id = way.start()
                if res_pi_id > 0:
                    if hasattr(way, "supplier_id"):
                        self.supplier_id = getattr(way, "supplier_id")
                    res_user_token_id = self._createdUserToken(
                        proxy_id=res_pi_id, network=way.network, is_cn_proxy=way.is_cn_proxy, is_more_sell=way.is_more_sell
                    )
                    break

            if res_user_token_id <= 0:
                raise NoProxyError()
        except Exception as e:
            if pay_order_id > 0:
                db.mysql.execute("update t_user_logs in_order set ip_id=-1 where id=%s",log_id)
                self.refund("refund_a_dynamic", pay_order_id=pay_order_id)
            log.exception(f"[byArea] Failed to create proxy. Error: {e}")
            return self.createProxyExceptWrapper(e)
        if res_user_token_id > 0:
            return self.returnSucceed({"token_id": res_user_token_id})
        else:
            if pay_order_id > 0:
                db.mysql.execute("update t_user_logs in_order set ip_id=-1 where id=%s", log_id)
                self.refund("refund_a_dynamic", pay_order_id=pay_order_id)
            log.error(f"[byArea] Failed to create proxy. Internal error. uid={self.uid}.")
            return self.returnError(constants.ERR_INTERNAL_ERROR, lang=self.lang)

class MakeDynamicIPProxyFromPool(MakeDynamicIPProxy):
    def byArea(self,country=None, state=None, city=None, network='http'):
        record = get_record_from_pool(country,state, city,self.uid)
        st = int(time.time())
        log_id = db.mysql.execute(
            "insert into t_user_logs (created_on,modified_on,uid,deleted_on,log_type_id,msg,ip_id,client_ip) values ("
            "%s,%s,%s,%s,%s,%s,%s,%s)", st, st, self.uid, 0, 101,
            f"country_id:{country},state:{state},city:{city}", 0, "")
        if record:
            if self.uid:
                pay_order_id = self.pay("buy_a_dynamic")
                if 0 >= pay_order_id:
                    log.error(f"[byArea] Failed to create proxy. Balance not enough. uid={self.uid}.")
                    return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH, lang=self.lang)

            pi_id =record.get("pi_id") or add_proxy_ips(pm_id=record.get("pm_id"),ip_id=record.get("ip_id"),port=record.get("port"),forward_port=record.get("port")
                                  ,country_id=record.get("country_id"),state_id=record.get("state_id"),city_id=record.get("city_id")
                                  ,health=True,online=True,status=True)
            update_pool_record(record.get("id"),pi_id)
            res_user_token_id = self._createdUserToken(
                proxy_id=pi_id, network=network
            )
            return self.returnSucceed({"token_id": res_user_token_id})
        else:
            return super().byArea(country=country, state=state, city=city, network=network)
class MakeStaticIPProxy(MakeProxy):
    def buy(self, proxy_id, life_time, network=None) -> int:
        if life_time < 1:
            raise TypeError()
        buy_static_proxy = Buy()
        buy_static_proxy.uid = self.uid
        buy_static_proxy.life_time = life_time
        buy_static_proxy.network = network
        buy_static_proxy.setProxyID(proxy_id)
        res_pi_id = buy_static_proxy.start()
        return (
            self._createdUserToken(
                proxy_id=res_pi_id,
                network=buy_static_proxy.network,
                is_cn_proxy=buy_static_proxy.is_cn_proxy,
                is_more_sell=buy_static_proxy.is_more_sell,
                is_static=True,
                life_time=buy_static_proxy.life_time,
            )
            if res_pi_id
            else 0
        )

    def rebuy(self, user_token_id, life_time) -> int:
        if life_time < 1:
            raise TypeError()
        rebuy_static_proxy = Rebuy()
        rebuy_static_proxy.uid = self.uid
        rebuy_static_proxy.life_time = life_time
        rebuy_static_proxy.setUserTokenID(user_token_id)
        res_pi_id = rebuy_static_proxy.start()
        if not res_pi_id:
            return 0
        return (
            user_token_id
            if change_user_token_created_on_and_expired_and_life_time(token_id=user_token_id, life_time=life_time)
            else 0
        )

    def renew(self, user_token_id, life_time) -> int:
        if life_time < 1:
            raise TypeError()
        renew_static_proxy = Renew()
        renew_static_proxy.uid = self.uid
        renew_static_proxy.life_time = life_time
        renew_static_proxy.setUserTokenID(user_token_id)
        res_pi_id = renew_static_proxy.start()
        if not res_pi_id:
            return 0
        return user_token_id if change_user_token_life_time(token_id=user_token_id, life_time=life_time) else 0


def only_country(country, state, city):
    if country and not state and not city:
        return True
    else:
        return False


class MakeTrafficProxy(MakeProxy):
    """
    它接受多个参数来指定代理的地理位置、协议类型和其他属性，然后使用 Luminati 或 Oxylabs 等服务商提供的 API 来购买相应的代理。
    """

    def __init__(self, is_static: bool, is_data_center: bool = False, uid=None, lang="us"):
        self.is_static, self.is_data_center = is_static, is_data_center
        super().__init__(uid=uid, lang=lang)

    def _userBalanceIsEnough(self):
        return not self.uid or get_balance(self.uid,db_session=self.db_session) >= 10

    def get_user_special_area(self,user_type=None,country=None,city=None):
        """
        返回la_ids
        """

        la_ids = []
        if country:
            tmp_ids = get_country_status(country,user_type)
            if tmp_ids:
                for i in tmp_ids:
                    la_ids.extend(json.loads(i["la_ids"]))
        if city:
            tmp_ids = get_city_status(city,user_type)
            if tmp_ids:
                for i in tmp_ids:
                    la_ids.extend(json.loads(i["la_ids"]))
        log.error(la_ids)
        return list(set(la_ids))

    def by_area(self, country=None, state=None, city=None, protocol="http", s=0):

        @cache(60*2)
        def _get_weights():
            record = db.mysql.query(
                "select  type,probability from t_sell_settings where type in ('weight_s1','weight_s2','weight_s3') and on_off=1 order by type asc")
            weights = [i.get("probability") if i.get("probability")<100 else 100  for i in record]
            return weights

        if not self._userBalanceIsEnough():
            log.error("用户余额不足")
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH, lang=self.lang)
        # if protocol != "http":
        #     log.error("只支持 HTTP 协议")
        #     return self.returnError(constants.ERR_PROXY_ONLY_HTTP, lang=self.lang)
        la_ids = []
        # 判断用户选的地区是不是特殊地区
        # s_type = get_state_by_area(country, city)
        # if s_type:
        #     # 判断用户有没有权限购买特殊地区
        #     # user_type = get_user_special_areas(self.uid)
        #     # if not user_type:
        #     #     log.error("用户没有权限购买特殊地区")
        #     #     return self.returnError(constants.ERR_PROXY_AREA_ERROR, lang=self.lang)
        #     # 获取用户对应的特殊地区
        #     la_ids = self.get_user_special_area(country=country,city=city,user_type=s_type['id'])


        if self.is_static:
            buy = BuyDataCenterStaticLumTrafficProxy(network_type=protocol) if self.is_data_center else BuyStaticLumTrafficProxy(network_type=protocol,s=s)
        else:
            tmp = only_country(country, state, city)
            weights = _get_weights()
            if s <0:
                buy = random.choice([BuyOxyTrafficProxy, BuyDynamicLumTrafficProxy])()
            elif 0 == s or 1 == s:
                if tmp:
                    list_class = [BuyOxyTrafficProxy, BuyLunaTrafficProxy]
                    buy = random.choices(list_class, [weights[0],100-weights[0]],k=1)[0](network_type=protocol)
                else:
                    buy = BuyOxyTrafficProxy(network_type=protocol)
            elif 2 == s:
                if tmp:
                    list_class = [BuyDynamicLumTrafficProxy, BuyOxyTrafficProxy]
                    buy = random.choices(list_class, [10,90],k=1)[0](network_type=protocol)
                else:
                    buy = BuyDynamicLumTrafficProxy(network_type=protocol)
            elif s == 3:
                if tmp:
                    list_class = [BuyLunaTrafficProxy, BuySmartTrafficProxy]
                    buy = random.choices(list_class, [weights[2],100-weights[2]],k=1)[0](network_type=protocol)
                else:
                    buy = BuyLunaTrafficProxy(network_type=protocol)
            elif s == 4:
                # list_class = [BuyNetnutTrafficProxy, BuySmartTrafficProxy]
                # buy = buy = random.choices(list_class, [50,50],k=1)[0](network_type=protocol)
                buy = BuySmartTrafficProxy(network_type=protocol)
            else:
                log.error("区域错误")
                return self.returnError(constants.ERR_AREA_ERROR, lang=self.lang)
        log.info(f"only country  buy:{buy.__class__.__name__}")

        buy.lang = self.lang
        buy.uid = self.uid
        buy.special_id_list = la_ids if not self.is_data_center else None
        try:
            buy.setArea(country, state, city)
        except Exception:
            log.error("设置区域时出错")
            return self.returnError(constants.ERR_AREA_ERROR, lang=self.lang)

        pi_id = buy.choose()
        if not pi_id or pi_id <= 0:
            log.error(buy.err)
            return buy.err or self.returnError(constants.ERR_INTERNAL_ERROR, lang=buy.lang)
        if hasattr(buy, "supplier_id"):
            self.supplier_id = getattr(buy, "supplier_id")
        token_id = self._createdUserToken(
            pi_id, is_static=self.is_static, la_id=buy._la_id, is_data_center=self.is_data_center,network=protocol
        )
        if token_id <= 0:
            log.error("创建用户令牌时出错")
            return self.returnError(constants.ERR_INTERNAL_ERROR, lang=buy.lang)
        return self.returnSucceed({"token_id": token_id})

    def make_api_proxy(self, only_country: bool = False, protocol: str = "http", s=2):
        if not self._userBalanceIsEnough():
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH, lang=self.lang)
        # if protocol != "http":
        #     return self.returnError(constants.ERR_PROXY_ONLY_HTTP, lang=self.lang)
        buy = BuyStaticLumAPIProxy() if self.is_static else BuyDynamicLumAPIProxy(network=protocol,s=s)  # type: BuyLumAPIProxy
        buy.lang = self.lang
        buy.is_only_country = only_country
        pi_id = buy.choose()
        if pi_id <= 0:
            print(buy.err)
            return buy.err or self.returnError(constants.ERR_INTERNAL_ERROR, lang=buy.lang)
        if hasattr(buy, "supplier_id"):
            self.supplier_id = getattr(buy, "supplier_id")
        self.username_and_password_length = (9, 16)
        token_id = self._createdUserToken(pi_id, is_static=self.is_static, la_id=buy.la_id, is_api=True,network=protocol)
        if token_id <= 0:
            return self.returnError(constants.ERR_INTERNAL_ERROR, lang=buy.lang)
        return self.returnSucceed({"token_id": token_id})
