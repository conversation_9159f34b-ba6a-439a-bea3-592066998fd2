from src.exceptions import *
from src.base import MakeProxyBase, ByUserTokenID
from models.proxy import *
import logging as log

class Renew(MakeProxyBase, ByUserTokenID):

    def __init__(self):
        self.setting = get_sell_setting("static_proxy")
        self.is_static = True

    def choose(self) -> int:
        if self.life_time < 1:
            raise TypeError()
        token_obj = get_user_token_by_id(self.user_token_id)
        if not token_obj or not token_obj['is_static'] or token_obj['uid'] != self.uid:  # 不是静态代理或者代理token不属于该用户
            raise NoThisTokenError()
        if token_obj['expired']:       # 代理token已经过期
            raise UserTokenExpiredError()
        proxy_obj = get_proxy_ip(token_obj['pi_id'])
        if not proxy_obj:
            raise NoProxyError()
        if not (proxy_obj['health'] and proxy_obj['online'] and proxy_obj['status']):   # 如果代理不是处于正常状态， 不允许续费
            raise ProxyNotOnlineError()
        return token_obj['pi_id']


import requests


def get_iproyal_order(order_id):
    # /iproyal/info/{order_id}
    url = f'https://dash-api.proxy302.com/api/iproyal/info/{order_id}'
    resp = requests.get(url)
    return resp.json()


def post_extend_iproyal_order(order_id):
    url = f'https://dash-api.proxy302.com/api/iproyal/order/{order_id}'
    resp = requests.post(url)
    if int(resp.status_code) != 200:
        return None
    resp_json = resp.json()
    return resp_json


def check_proxy_by_token_id(token_id):
    log.info(f"获取代理token_id: {token_id}")
    order = db.mysql.query("""
    select order_id from t_iproyal_orders left join t_user_tokens on t_iproyal_orders.pi_id = t_user_tokens.pi_id where
    t_user_tokens.id = %s

    """, token_id)
    if not order:
        return
    order_id = order[0]["order_id"]
    log.info(f"获取订单order_id: {order_id}")
    iproyal_order = get_iproyal_order(order_id)
    log.info(f"获取iproyal_order详情: {iproyal_order}")
    expireDate = iproyal_order.get("expire_date")
    import datetime
    expire_date = datetime.datetime.strptime(expireDate, "%Y-%m-%d %H:%M:%S")
    now_date = datetime.datetime.now()
    total = (expire_date - now_date).total_seconds()
    if total < 3600*24:
        """距离过期还有一天时间，续费"""
        post_extend_iproyal_order(order_id)
        log.info(f"更新订单order_id: {order_id} 成功续费30天")
        # 修改记录


