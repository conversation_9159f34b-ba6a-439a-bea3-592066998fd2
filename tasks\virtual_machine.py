from app import app
import time
import logging
from src import exceptions
from src.vm import VM



@app.task
def list_vm(uid:int=None, lang:str='us'):
    """
    获取用户的所有虚拟机列表
    :param uid:     :type int   用户ID， ID为None为所有用户的虚拟机内部使用
    :param lang:    :type str   语言
    :return:        :type dict
    """
    return VM(uid=uid, lang=lang).vms()

@app.task
def new_vm(vm_username:str, vm_password:str, payway_id: int, country_id: int = 0, auto_renew: bool=False, uid:int=None, lang:str='us'):
    """
    创建虚拟机
    :param vm_username:     :type str   虚拟机的用户名
    :param vm_password:     :type str   虚拟机的密码
    :param payway_id:       :type int   创建机器的套餐的payway ID
    :param country_id:      :type int   创建的VM的国家id,默认0随机
    :param auto_renew       :type bool  是否自动续费
    :param uid:             :type int   用户ID， ID为None为内部使用
    :param lang:            :type str   语言
    :return:                :type dict
    """
    return VM(uid=uid, lang=lang).create(username=vm_username,
                                         password=vm_password,
                                         payway_id=payway_id,
                                         country_id=country_id,
                                         auto_renew=auto_renew)


@app.task
def control_vm(id:int, action:str, uid:int=None, lang:str='us'):
    """
    操作虚拟机
    :param id:      :type int   虚拟机ID
    :param action:  :type str   操作类型['startup', 'start', 'shutdown', 'stop', 'restart']
    :param uid:     :type int   用户ID， ID为None为内部使用
    :param lang:    :type str   语言
    :return:
    """
    vm = VM(uid=uid, lang=lang)
    action = action.lower()
    if action == "startup" or action == "start":
        return vm.startup(token_id=id)
    elif action == "shutdown" or action == "stop":
        return vm.shutdown(token_id=id)
    elif action == "restart":
        return vm.restart(token_id=id)
    else:
        raise ValueError()


@app.task
def renew_vm(id:int, payway_id:int, uid:int=None, lang:str='us'):
    """
    续费虚拟机
    :param id:          :type str   虚拟机ID
    :param payway_id:   :type int   续费的套餐payway ID
    :param uid:         :type int   用户ID， ID为None为内部使用
    :param lang:        :type str   语言
    :return:            :type dict
    """
    return VM(uid=uid, lang=lang).renew(token_id=id, payway_id=payway_id)


@app.task
def set_vm_auto_renew(id:int, on_off:bool, payway_id:int=None, uid:int=None, lang:str='us'):
    """
    续费虚拟机
    :param id:          :type str   虚拟机ID
    :param on_off:      :type bool  开启或关闭自动续费
    :param payway_id:   :type int   对应自动续费的套餐， 关闭自动续费的时候此参数无效
    :param uid:         :type int   用户ID， ID为None为内部使用
    :param lang:        :type str   语言
    :return:            :type dict
    """
    return VM(uid=uid, lang=lang).set_auto_renew(token_id=id, on_off=on_off, payway_id=payway_id)
