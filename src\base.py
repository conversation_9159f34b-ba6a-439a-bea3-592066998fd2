import abc
from src.exceptions import *
from utils.functions import area_verify, ip_verify


class ResponseBase:
    __lang_idx = {"cn": 1, 'us': 2}

    def returnError(self, err, append_msg="", msg="", data=None, lang=None):
        if not lang:
            lang = self.lang if hasattr(self, 'lang') else 'us'
        msg = msg or (err[self.__lang_idx.get(lang, 2)] + append_msg)
        return {'code': err[0], 'msg': msg, 'data': data or {}}

    def returnSucceed(self, data: dict=None, msg="succeed"):
        return {'code': 0, 'msg': msg, 'data': data or {}}


class ProxyBase:
    _err: dict = None
    _lang: str = 'us'
    _uid: int = None
    _network: str = 'http'

    @property
    def err(self):
        return self._err

    def set_err(self, err, append_msg="", msg="", data=None):
        lang_idx = {"cn": 1, 'us': 2}
        msg = msg or err[lang_idx.get(self._lang, 2)] + append_msg
        self._err = {'code': err[0], 'msg': msg, data: data or {}}

    @property
    def lang(self):
        return self._lang

    @lang.setter
    def lang(self, _lang):
        if _lang.lower() in ('cn', 'zh-cn', 'zh'):
            self._lang = 'cn'
        elif _lang.lower() in ('us', 'en', 'en-us'):
            self._lang = 'us'
        else:
            self._lang = 'us'

    @property
    def uid(self):
        return self._uid

    @uid.setter
    def uid(self, _uid):
        if _uid and not isinstance(_uid, int):
            raise UserError()
        self._uid = _uid

    @property
    def network(self):
        return self._network

    @network.setter
    def network(self, _network=None):
        if not _network:
            self._network = 'http'
        elif _network not in ('http', 'socks', 'socket', 'socks5'):
            raise NetworkTypeError()
        self._network = _network


class MakeProxyBase(ProxyBase):
    area_ids = [0, 0, 0]

    _is_cn_proxy: bool = None
    _is_more_sell: bool = False
    _is_static: bool = False
    _is_data_center: bool = False
    _life_time: int = 0
    _la_id: int = 0

    @property
    def is_cn_proxy(self):
        return self._is_cn_proxy

    @is_cn_proxy.setter
    def is_cn_proxy(self, _is_cn_proxy):
        if not isinstance(_is_cn_proxy, bool):
            raise TypeError()
        self._is_cn_proxy = _is_cn_proxy

    @property
    def is_static(self):
        return self._is_static

    @is_static.setter
    def is_static(self, _is_static):
        if not isinstance(_is_static, bool):
            raise TypeError()
        self._is_static = _is_static

    @property
    def is_data_center(self):
        return self._is_static

    @is_data_center.setter
    def is_data_center(self, _is_data_center):
        if not isinstance(_is_data_center, bool):
            raise TypeError()
        self._is_data_center = _is_data_center

    @property
    def life_time(self):
        return self._life_time

    @life_time.setter
    def life_time(self, _life_time):
        if _life_time > 0:
            self._life_time = _life_time

    @property
    def is_more_sell(self):
        return self._is_more_sell

    @is_more_sell.setter
    def is_more_sell(self, _is_more_sell):
        if not isinstance(_is_more_sell, bool):
            raise TypeError()
        self._is_more_sell = _is_more_sell

    @abc.abstractmethod
    def choose(self) -> int:
        pass

    # @task_response
    def start(self) -> int:
        return self.choose()


class ByIP:
    ip_addr, fuzzy_query = None, False

    def setIP(self, ip_addr):
        if type(ip_addr) == str and len(ip_addr.split("*")) > 1:
            self.ip_addr, self.fuzzy_query = ip_addr, True
        else:
            try:
                self.ip_addr = ip_verify(ip_addr)
            except:
                raise TypeError(": IP")


class ByArea:
    country, state, city = (0, ""), (0, ""), (0, "")

    def setArea(self, country, state, city):
        """
        设置要选择的ip的地区，字符串长度为0的时候为随即
        :param country:
        :param state:
        :param city:
        """
        self.country, self.state, self.city = area_verify(country, state, city)


class ByProxyID:
    proxy_id = 0

    def setProxyID(self, proxy_id):
        self.proxy_id = proxy_id


class ByUserTokenID:
    user_token_id = 0

    def setUserTokenID(self, user_token_id):
        self.user_token_id = user_token_id
