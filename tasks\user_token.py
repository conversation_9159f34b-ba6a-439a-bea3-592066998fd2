from app import app
from src import *
from src.set_proxy import SetTokenRemark


@app.task
def getUserTokens():
    pass


@app.task
def set_user_token_remark(token_id:int, content:str="", uid:int=None, lang='us') -> dict:
    """
    修改User Token备注
    :param token_id:    :type: str      user token id
    :param content:     :type: str      备注内容，为空字符串的时候是删除
    :param uid:         :type: int      用户ID。  default:None(内部使用 uid=0) 创建测试用的token,
    :param lang:        :type: str      语言
    :return:
    """
    return SetTokenRemark(token_id=token_id, uid=uid, lang=lang).do(content=content)
