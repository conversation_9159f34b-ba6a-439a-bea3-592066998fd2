import json
import time
import db
import re
import datetime
import traceback
from typing import Union

from log import get_logger

log = get_logger(__name__)

from utils.decorators import cache


def get_payway_by_id(payway_id: int) -> dict:
    return db.mysql.get("SELECT * FROM t_payways WHERE id = %s AND deleted_on = 0", payway_id)

def get_vm_payway(payway:str, vm_level_id:int, days:int) -> dict:
    return db.mysql.get("""
        SELECT * FROM t_payways WHERE deleted_on = 0 AND payway = %s AND vm_level_id = %s AND days = %s
        ORDER BY created_on DESC LIMIT 1
    """, payway, vm_level_id, days)

@cache(60*15)
def get_payway(payway: str) -> dict:
    res = db.mysql.get("SELECT * FROM t_payways WHERE payway = %s AND deleted_on = 0", payway)
    return res


def get_charge_ways() -> list:
    return db.mysql.query(f"""
        SELECT * FROM t_payways WHERE deleted_on = 0 AND currency != '' 
    """)


def get_last_order(uid):
    return db.mysql.get("""
        SELECT total_money, balance FROM t_ip_orders 
        WHERE deleted_on = 0 AND uid = %s AND status != 0 AND valid = true  ORDER BY orderid DESC LIMIT 1
    """, uid)


def chang_ip_order_status(orderid, status: int):
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_ip_orders SET status = %s, modified_on = %s WHERE deleted_on = 0 AND orderid = %s 
        """, status, t, orderid)
        return effect > 0
    except:
        traceback.print_exc()
        return False\


def set_ip_order(uid: int, type: str, currency_type: str, currency: int, payway: str, pay_order: str, value:int,
                 extra_value:int = 0, receive_currency: int = 0, status: int = 0, checksum: bool = True,
                 valid: bool = False, is_inner: bool = False, ut_id = 0, db_session=None) -> int:
    """

    :param uid:
    :param type:
    :param currency_type:
    :param currency:
    :param receive_currency:
    :param payway:
    :param pay_order:
    :param amount:
    :param status:
    :param checksum:
    :param valid:
    :param is_inner:
    :return:    失败返回0  成功返回orderID
    """
    db_mysql = db_session or db.mysql
    if type not in ['+', '-']:
        return 0
    order_id = int(re.sub(r"-|:|\.| ", '', str(datetime.datetime.now()))) // 100
    last_order = db_mysql.get("""
        SELECT total_money, balance FROM t_ip_orders 
        WHERE deleted_on = 0 AND uid = %s AND status != 0 AND valid = true  ORDER BY orderid DESC LIMIT 1
    """, uid)
    total_money, balance, amount = 0, 0, value + extra_value
    if last_order:
        if type == "+":
            total_money, balance = last_order['total_money'] + amount, last_order['balance'] + amount
        elif type == "-":
            total_money, balance = last_order['total_money'], last_order['balance'] - amount
    else:
        if type == "+":
            total_money, balance = amount, amount
        elif type == "-":
            total_money, balance = 0, -amount
    if balance < 0:
        return 0
    t = int(time.time())
    try:
        db_mysql.execute("""
            INSERT INTO t_ip_orders(orderid, created_on, modified_on, deleted_on, uid, `type`, currency_type, currency, 
            receive_currency, payway, pay_order, value, extra_value, amount, balance, total_money, status, checksum, 
            valid, ut_id, is_inner ) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, order_id, t, t, 0, uid, type, currency_type, currency, receive_currency, payway, pay_order,
        value, extra_value, amount, balance, total_money, status, checksum, valid, ut_id, is_inner)
        db_mysql.execute("""
            UPDATE t_users_info SET balance = %s, total_balance = %s, ex_balance = %s, modified_on = %s WHERE uid = %s
        """, balance, total_money, total_money - balance, t, uid)
        return order_id
    except:
        log.error(traceback.format_exc())
        return 0


def add_ready_ip_order(uid: int, type: str, currency_type: str, currency: int, payway: str, pay_order: str,
                       value:int, extra_value:int = 0, receive_currency: int = 0, status: int = 0,
                       checksum: bool = True, valid: bool = False, is_inner: bool = False) -> int:
    if type not in ['+', '-']:
        return 0
    order_id = int(re.sub(r"-|:|\.| ", '', str(datetime.datetime.now()))) // 100
    t = int(time.time())
    try:
        db.mysql.execute("""
            INSERT INTO t_ip_orders(orderid, created_on, modified_on, deleted_on, uid, `type`, currency_type, currency, 
            receive_currency, payway, pay_order, value, extra_value, amount, balance, total_money, status, checksum, valid, is_inner ) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, order_id, t, t, 0, uid, type, currency_type, currency, receive_currency, payway, pay_order,
        value, extra_value, value + extra_value, 0, 0, status, checksum, valid, is_inner)
        return order_id
    except:
        log.error(traceback.format_exc())
        return 0


def set_order_user_token_id(order_id:int, ut_id:int) -> bool:
    try:
        effect = db.mysql.execute_rowcount(""" 
            UPDATE t_ip_orders SET ut_id = %s WHERE orderid = %s AND deleted_on = 0 
        """, ut_id, order_id)
        return effect > 0
    except:
        traceback.print_exc()
        return False


def change_ip_order_status_by_order_id(order_id:int, status: int, valid:bool=True) -> bool:
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_ip_orders SET modified_on = %s, status = %s, valid = %s WHERE deleted_on = 0 AND orderid = %s
        """, t, status, valid, order_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def change_ip_order_status_by_pay_order(pay_order: str, receive_currency: int, status: int, valid: bool, text="") -> dict or None:
    """

    :param pay_order:
    :param receive_currency:
    :param status:
    :param valid:
    :return:   返回那一条记录， 错误返回 None
    """
    t = int(time.time())
    try:
        ip_order = db.mysql.get("SELECT * FROM t_ip_orders WHERE deleted_on = 0 AND pay_order = %s", pay_order)
        if not ip_order:
            raise Exception(f"not find {pay_order}")
        if ip_order['status'] != 0:
            raise Exception(f"ip order {pay_order}  ID:{ip_order['orderid']} has completed. ")
        uid, amount, type = ip_order['uid'], ip_order['amount'], ip_order['type']
        if type not in ('+', '-'):
            raise Exception(f"type error, not allow type={type}")
        last_order = db.mysql.get("""
            SELECT total_money, balance FROM t_ip_orders 
            WHERE deleted_on = 0 AND uid = %s AND status != 0 AND valid = true ORDER BY orderid DESC LIMIT 1
        """, uid)
        total_money, balance = 0, 0
        if last_order:
            if type == "+":
                total_money, balance = last_order['total_money'] + amount, last_order['balance'] + amount
            elif type == "-":
                total_money, balance = last_order['total_money'], last_order['balance'] - amount
        else:
            if type == "+":
                total_money, balance = amount, amount
            elif type == "-":
                total_money, balance = 0, -amount
        if balance < 0:
            raise Exception("balance error")
        order_id = int(re.sub(r"-|:|\.| ", '', str(datetime.datetime.now()))) // 100
        db.mysql.execute("""
            UPDATE t_ip_orders SET orderid = %s, modified_on = %s,  receive_currency = %s,
            status = %s, valid = %s, balance = %s, total_money = %s, text = %s 
            WHERE  deleted_on = 0 AND pay_order = %s 
        """,order_id, t, receive_currency, status, valid, balance, total_money, text, pay_order)
        db.mysql.execute("""
            UPDATE t_users_info SET balance = %s, total_balance = %s, ex_balance = %s, modified_on = %s WHERE uid = %s
        """, balance, total_money, total_money - balance, t, uid)
        return ip_order
    except:
        log.error(traceback.format_exc())
        return None


def get_ip_order(uid: int, payway_id_list) -> list:
    return db.mysql.query(f"""
        SELECT t_ip_orders.created_on, orderid, note, en_note, amount, t_payways.type, 
        t_ip_orders.payway, t_ip_orders.currency FROM t_ip_orders 
        LEFT JOIN t_payways ON t_ip_orders.payway = t_payways.payway 
        WHERE t_ip_orders.deleted_on = 0 AND uid = %s AND status = 1 AND valid = 1 AND 
        t_payways.id IN ({ ",".join([str(i) for i in payway_id_list]) })
        ORDER BY orderid ASC
    """, uid)


def get_ip_order_by_types(uid:int, payway_types:Union[str, list, tuple],
                          after_ts:int=None, before_ts:int=None) -> Union[None, list]:
    if type(payway_types) == str:
        payway_types = (payway_types, )
    elif isinstance(payway_types, (tuple, list)):
        payway_types = tuple(payway_types)
    else:
        return None
    after_ts = after_ts or int(time.time())     # 默认当前时间
    before_ts = before_ts or after_ts - 60 * 60 * 24 * 190 # 默认显示90天内的数据
    return db.mysql.query(f"""
        SELECT t_ip_orders.created_on, orderid, note, en_note, amount, t_payways.type, t_ip_orders.traffic_usage,
        t_ip_orders.payway, t_ip_orders.currency, INET6_NTOA(UNHEX(ip_address)) AS proxy_ip FROM t_ip_orders 
        LEFT JOIN t_payways ON t_ip_orders.payway = t_payways.payway 
        LEFT JOIN t_user_tokens ON ut_id = t_user_tokens.id
        LEFT JOIN t_proxy_ips ON t_proxy_ips.id = pi_id
        LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id
        WHERE t_ip_orders.deleted_on = 0 AND t_ip_orders.uid = %s AND t_ip_orders.status = 1 AND valid = 1 
        AND t_ip_orders.created_on BETWEEN %s AND %s AND t_payways.payway IN %s ORDER BY orderid 
    """, uid, before_ts, after_ts, payway_types)


def get_charge_bill(uid: int) -> list:
    return db.mysql.query("""
        SELECT t_ip_orders.created_on, orderid, note, en_note, value, t_ip_orders.extra_value, amount, 
        t_ip_orders.type, t_ip_orders.payway, currency_type,  t_ip_orders.currency FROM t_ip_orders 
        LEFT JOIN t_payways ON t_ip_orders.payway = t_payways.payway 
        WHERE t_ip_orders.deleted_on = 0 AND uid = %s AND status = 1 AND valid = 1 AND currency_type != ''
    """, uid)


def is_first_charge(uid:int) -> bool:
    charge_order_total = db.mysql.get("""
        SELECT COUNT(1) FROM t_ip_orders WHERE uid = %s AND deleted_on = 0 
        AND status = 1 AND valid = 1 AND payway LIKE 'charge%%'
    """, uid)
    return charge_order_total['COUNT(1)'] <= 1


def is_first_change_1_usd(uid:int) -> bool:
    change_1_used_total =  db.mysql.get("""
        SELECT COUNT(1) FROM t_ip_orders LEFT JOIN t_payways ON t_payways.payway = t_ip_orders.payway
        WHERE t_ip_orders.deleted_on = 0 AND uid = %s AND price = 1 AND receive_currency = 1 
        AND t_payways.currency = 'usd' AND status != 0 AND valid = 1
    """, uid)
    # print(change_1_used_total)
    return change_1_used_total['COUNT(1)'] < 1


def get_count_user_buy_payway(uid:int, payway:str) -> int:
    return db.mysql.get("""
        SELECT COUNT(1) FROM t_ip_orders WHERE deleted_on = 0 AND uid = %s AND payway = %s AND status > 0 AND valid = 1
    """, uid, payway)['COUNT(1)']


def set_user_traffic_proxy_expired(uid, expired=True):
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_user_tokens SET expired = %s, modified_on = %s 
            WHERE uid = %s AND deleted_on = 0 AND la_id > 0 AND expired != %s
        """, expired, t, uid, expired)
        return effect > 0
    except:
        traceback.print_exc()
        return False