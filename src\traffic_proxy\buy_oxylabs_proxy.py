import abc
from typing import Union
import json
import random
import requests
import constants
from typing import Optional, List
from libs import torndb
from models.proxy import *
from src.base import MakeProxyBase, ByProxyID, ByArea
from src.exceptions import *
from utils.functions import randomString
from log import get_logger

log = get_logger(__name__)

class BuyOxyTrafficProxy(MakeProxyBase, ByArea):
    def __init__(self, network_type: str = "http"):
        self.network_type = network_type
        
    def _testProxy(self, username: str, password: str, port: int = 7777,
                   host: str = "pr.oxylabs.io", test_url: str = "http://ipinfo.io",
                   extract_ip_fun=lambda x: json.loads(x)['ip']) -> Union[str, None]:
        try:
            proxy = f"http://{username}:{password}@{host}:{port}"
            log.info(f"test oxylabs proxy: {proxy}")
            res = requests.get(url=test_url, proxies={'http': proxy, 'https': proxy}, timeout=20)
            res.raise_for_status()
            log.info(res.text)
            return extract_ip_fun(res.text) if extract_ip_fun else ""
        except Exception as e:
            log.warning(e)
            return None

    @staticmethod
    def use_luminati_get_city_by_state(country_code: str, state_code: str, url: str = "http://ipinfo.io",
                                       extract_city_fun=lambda x: json.loads(x)['city'],
                                       lum_username: str = "brd-customer-c_3c2c92d3-zone-p_dynamic_traffic_city",
                                       lum_password: str = "css3nkt4x1a4") -> str:
        """
        通过luminati获得city
        :param country_code:
        :param state_code:
        :param url:                 获取地理位置的接口url
        :param extract_city_fun:    提取城市的函数
        :param lum_username:        luminati zone 用户名
        :param lum_password:        luminati zone 密码
        :return:
        """
        try:
            lum_proxy = f"http://{lum_username}-country-{country_code}-state-{state_code}" \
                        f":{lum_password}@brd.superproxy.io:22225"
            resp = requests.get(url, timeout=5, proxies={'http': lum_proxy, 'https': lum_proxy})
            resp.raise_for_status()
            return extract_city_fun(resp.text) if extract_city_fun else ""
        except Exception as e:
            log.warning(e)
            return ""

    def connectToken(self) -> str:
        """ 拼接token """
        token = f"-cc-{self.country[1].lower()}" if self.country[1] else ""     # 1 精确到country

        if self.state[0] != 0:                                                # 3 精确到state
            if self.country[1] == "US":                                         # 3.1 country == US -> username上拼接 -state-us_xxx
                token += f"-st-us_{self.state[2].replace(' ', '_').lower()}"
                if self.city[0] != 0:  # 2 精确到city
                    token += f"-city-{self.city[1].replace(' ', '_').lower()}"
            else:                                                               # 3.2 country != US -> 有城市：随机一个城市拼接，没城市：返回生成失败
                city = self.use_luminati_get_city_by_state(country_code=self.country[1], state_code=self.state[1])
                if city:
                    token += f"-city-{city.replace(' ', '_').lower()}"
                else:
                    return ""
        return token

    def choose(self) -> int:
        oxylab_obj = get_luminati_account(is_oxylabs=True)  # type: Optional[dict]  # get oxylab proxy
        if not oxylab_obj:
            self.set_err(constants.ERR_MACHINE_DEFICIENCY)
            return 0
        pm_id = oxylab_obj['pm_id']
        if pm_id:  # 指定了使用哪个luminati host
            machine_obj = get_machine_by_id(pm_id)
            if not machine_obj:
                self.set_err(constants.ERR_MACHINE_DEFICIENCY)
                return 0
        else:  # 没有指定 选取第一个
            machine_objs = get_machines_by_supplier("luminati")
            if not machine_objs:
                self.set_err(constants.ERR_MACHINE_DEFICIENCY)
                return 0
            machine_obj = machine_objs[0]
        token = self.connectToken()
        if not token:
            self.set_err(constants.ERR_MACHINE_DEFICIENCY)
            return 0
        self.supplier_id = machine_obj['ps_id']
        forward_port = machine_obj['forward_port_start'] or machine_obj['forward_port_end']
        wrapped_username = oxylab_obj['username'] + token + "-sessid-" + randomString(6, uppercase=False)
        # if self._testProxy(username=wrapped_username, password=oxylab_obj['password']):  # 动态ip就是单纯的字符串拼接，不需要校验
        self._la_id = oxylab_obj['id']
        db_session = torndb.Connection(**constants.mysql_config)
        return add_proxy_ips(ip_id=0, pm_id=machine_obj['id'], forward_port=forward_port,
                             country_id=self.country[0], state_id=self.state[0], city_id=self.city[0],
                             online=True, status=True, health=True, username=wrapped_username,
                             password=oxylab_obj['password'], db_session=db_session)
