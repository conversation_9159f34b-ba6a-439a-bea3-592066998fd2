from app import app
from src import *

from datetime import datetime


@app.task
def make_proxy_by_area(country=None, state=None, city=None, uid=None, lang="us",protocol="http",**kwargs) -> dict:
    """
    通过地区生成动态流量代理
    :param country:         :type: str | int    国家，可以使用country数据库中的id或者国家的缩写  default: None(0):随机
    :param state:           :type: str | int    省/州，可以使用state数据库中的id或者省/州的缩写  default: None(0):随机
    :param city:            :type: str | int    城市，可以使用city数据库中的id或者城市的英文  default: None(0):随机
    :param uid:             :type: int          用户ID。  default:None(内部使用 uid=0) 创建测试用的token,
    :param lang:            :type: str          语言。 'us':英文,'zh':中文  default:‘us’ 参数是不支持语言的时候为英文
    :return:                :type: dict
    """
    start_time = datetime.now()
    m = MakeTrafficProxy(is_static=True, is_data_center=True, uid=uid, lang=lang)
    result = m.by_area(country, state, city,protocol)
    return result
