from datetime import datetime, date

import os

import time
import traceback
import pytz
from dateutil.relativedelta import relativedelta
from log import get_logger
from utils.decorators import cache

log = get_logger(__name__)
import db


def get_user_by_uid(uid):
    return db.mysql.get(""" SELECT * FROM t_users WHERE deleted_on = 0 AND uid = %s """, uid)


def set_user_log(uid) -> bool:
    try:
        effect = db.mysql.execute_rowcount("""
            INSERT INTO t_user_logs VALUES ()
        """)
        return effect > 0
    except:
        traceback.print_exc()
        return False


def get_user_special_areas(uid):
    """
    获取用户的特殊地区
    """
    return db.mysql.get("""
        SELECT * FROM t_users WHERE deleted_on = 0 AND uid = %s
    """, uid)["could_get_special_areas"]


def get_balance(uid, db_session=None):
    db_mysql = db_session or db.mysql
    last_order = db_mysql.get("""
        SELECT total_money, balance FROM t_ip_orders 
        WHERE deleted_on = 0 AND uid = %s AND status != 0 AND valid = true  ORDER BY orderid DESC LIMIT 1
    """, uid)
    db_mysql.execute_rowcount("""
        UPDATE t_users_info SET balance = %s, total_balance = %s, ex_balance = %s  
        WHERE uid = %s AND deleted_on = 0 AND balance != %s
    """, last_order['balance'], last_order['total_money'], last_order['total_money'] - last_order['balance'],
                              uid, last_order['balance'])
    return last_order['balance']
