import ipaddress
import socket

from datetime import datetime, date

import os
from typing import List, Union, Optional
import time
import traceback
import pytz
import requests
from dateutil.relativedelta import relativedelta
import logging as log
import db
from utils.decorators import timer


def add_ip_address(ip_address):
    try:
        ip_addr = ipaddress.ip_address(ip_address)
        ip_obj = db.mysql.get(""" 
            SELECT * FROM t_ips WHERE deleted_on = 0 AND CAST(CONV(ip_address, 16, 10) AS SIGNED) = %s
        """, int(ip_addr))
        if ip_obj:
            return ip_obj['id']
        t = int(time.time())
        return db.mysql.execute("""
            INSERT INTO t_ips(created_on, modified_on, deleted_on, ip_addr, ip_address) 
            VALUES (%s, %s, %s, %s, HEX(INET6_ATON(%s)))
        """, t, t, 0, int(ip_addr) if type(ip_addr) == ipaddress.IPv4Address else 0, str(ip_addr))
    except:
        traceback.print_exc()
        return None


## 超卖相关 start

def get_ip(ip_code: int) -> dict:
    return db.mysql.get("SELECT * FROM t_ips WHERE deleted_on = 0 AND ip_addr = %s", ip_code)


def get_available_ip(ip_code: int) -> list:
    return db.mysql.query("""
        SELECT t_proxy_ips.id, ip_id, INET6_NTOA(UNHEX(ip_address)) as ip_address FROM t_proxy_ips
        LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id
        WHERE port = 0 AND online = 1 AND status = 1 AND health = 1 
        AND t_proxy_ips.deleted_on = 0 AND CAST(CONV(ip_address, 16, 10) AS SIGNED) = %s ORDER BY t_proxy_ips.id DESC
    """, ip_code)


def get_ip_fuzzy_query(ip_address: str) -> list:
    ip_address = ip_address.replace("*", "%%")
    return db.mysql.query("""
        SELECT id, INET6_NTOA(UNHEX(ip_address)) as ip_address FROM t_ips 
        WHERE deleted_on = 0 AND INET6_NTOA(UNHEX(ip_address)) LIKE %s ORDER BY created_on DESC 
    """, ip_address)


def get_available_ip_fuzz_query(ip_address: str) -> list:
    ip_address = ip_address.replace("*", "%%")
    return db.mysql.query("""
        SELECT t_proxy_ips.id, ip_id, INET6_NTOA(UNHEX(ip_address)) as ip_address FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id 
        WHERE t_proxy_ips.deleted_on = 0 AND port = 0 AND online = 1 AND status = 1 AND health = 1 
        AND INET6_NTOA(UNHEX(ip_address)) LIKE %s ORDER BY t_proxy_ips.id DESC
    """, ip_address)


def get_ip_using_condition(ip_id: int) -> dict:
    return db.mysql.get("""
        SELECT * FROM t_proxy_ips WHERE deleted_on = 0 
        AND online = 1 AND status = 1 AND health = 1
        AND ip_id = %s ORDER BY created_on DESC LIMIT 1
    """, ip_id)


def get_conform_area_proxy(country_id: int = 0, state_id: int = 0, city_id: int = 0, is_911: bool = None,**kwargs) -> list:
    uid = kwargs.get("uid")
    extra_sql = ""
    if uid:
        extra_sql = f" AND  t_proxy_ips.id not in (select pi_id from t_user_tokens where uid= {uid}" \
                    f" and deleted_on = 0 and is_static=0 ) "
    sql = f"""
        SELECT t_proxy_ips.id, pm_id, t_proxy_ips.forward_port, port, t_proxy_ips.ip_id, 
        online, status, health, country_id, state_id, city_id FROM t_proxy_ips 
        LEFT JOIN t_user_tokens ON t_user_tokens.pi_id = t_proxy_ips.id 
        LEFT JOIN t_proxy_machines ON t_proxy_ips.pm_id = t_proxy_machines.id 
        LEFT JOIN t_proxy_suppliers ON t_proxy_machines.ps_id = t_proxy_suppliers.id 
        WHERE expired = 0 AND t_proxy_ips.deleted_on = 0 AND status = 1 AND online = 1 AND health = 1 
        {"AND  t_proxy_suppliers.name = '911'" if is_911 is not None and is_911 else ""}
        {f"AND country_id = {country_id}" if country_id else ""}
        {f"AND state_id = {state_id}" if state_id else ""}
        {f"AND city_id = {city_id}" if city_id else ""} 
        {extra_sql}
        ORDER BY t_proxy_ips.created_on DESC
    """
    log.info(sql)
    return db.mysql.query(sql)


def get_proxy_using_condition(pi_id) -> list:
    return db.mysql.query("SELECT * FROM t_user_tokens WHERE deleted_on = 0 AND pi_id = %s", pi_id)


def get_proxy_using_unexpired_condition(pi_id) -> list:
    return db.mysql.query(""" SELECT * FROM t_user_tokens WHERE deleted_on = 0 AND pi_id = %s AND expired = 0 """,
                          pi_id)


def get_proxy_by_ip_id(ip_id: int) -> dict:
    return db.mysql.get("""
        SELECT * FROM t_proxy_ips WHERE deleted_on = 0 
        AND online = 1 AND status = 1 AND health = 1 
        AND ip_id = %s ORDER BY created_on DESC LIMIT 1 
    """, ip_id)


def get_sell_setting_by_type(sell_type: str) -> dict:
    return db.mysql.get("SELECT * FROM t_sell_settings WHERE deleted_on = 0 AND type = %s", sell_type)


def get_sell_setting(setting_type) -> dict:
    setting = db.redis.hgetall('sell_setting_' + setting_type)
    if setting:
        return {'on_off': setting[b'on_off'],
                'how_many': int(setting[b'how_many']),
                'how_long': int(setting[b'how_long']),
                'probability': float(setting[b'probability'])}
    setting = db.mysql.get(""" SELECT * FROM t_sell_settings WHERE deleted_on = 0 AND type = %s """, setting_type)
    if not setting:
        return {}
    setting = {'on_off': setting['on_off'],
               'how_many': setting['how_many'],
               'how_long': setting['how_long'],
               'probability': float(setting['probability'])}
    db.redis.hset('sell_setting_' + setting_type, mapping=setting)
    db.redis.expire('sell_setting_' + setting_type, 60 * 60)
    return setting


## 超卖相关 end

## 国内代理相关 start

def get_user_using_proxy(uid: int) -> list:
    return db.mysql.query("SELECT * FROM t_user_tokens LEFT JOIN t_proxy_ips ON t_user_tokens.pi_id = t_proxy_ips.id "
                          "WHERE t_user_tokens.deleted_on = 0 AND t_proxy_ips.deleted_on = 0 "
                          "AND expired = 0 AND online = 1 AND status = 1 AND uid = %s", uid)


def get_user_used_proxies(uid: int, hour: int) -> list:
    now_ts, second = int(time.time()), hour * 60 * 60
    before = now_ts - second
    return db.mysql.query("""
        SELECT * FROM t_user_tokens LEFT JOIN t_proxy_ips ON t_user_tokens.pi_id = t_proxy_ips.id
        WHERE t_user_tokens.deleted_on = 0 AND t_proxy_ips.deleted_on = 0
        AND status = 1 AND uid = %s AND t_user_tokens.created_on > %s
    """, uid, before)


def catch_cn_proxy(country_id: int = 0, state_id: int = 0, city_id: int = 0, ps_id: int = 0,
                   without_ip_ids=None,**kwargs) -> list:
    if without_ip_ids is None:
        without_ip_ids = []
    uid = kwargs.get("uid")

    sql = "SELECT t_proxy_ips.id, t_proxy_ips.ip_id FROM t_proxy_ips " \
          "LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id " \
          "WHERE t_proxy_ips.deleted_on = 0 AND online = 1 AND status = 1 AND health = 1"
    if ps_id:
        sql += f" AND ps_id = {ps_id}"
    if country_id:
        sql += f" AND country_id = {country_id} "
        if state_id:
            sql += f" AND state_id = {state_id} "
            if city_id:
                sql += f" AND city_id = {city_id} "
    if without_ip_ids:
        sql += f" AND t_proxy_ips.ip_id NOT IN ({','.join([str(i) for i in without_ip_ids])}) "
    if uid:
        sql += f" AND  t_proxy_ips.id not in (select pi_id from t_user_tokens where uid= {uid}" \
                    f" and deleted_on = 0 and is_static=0 ) "
    log.info(sql)
    return db.mysql.query(sql)


def get_cn_proxy_by_ip_id(ip_id: int, ps_id: int) -> dict:
    return db.mysql.get("SELECT t_proxy_ips.id, pm_id FROM t_proxy_ips "
                        "LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id "
                        "WHERE t_proxy_ips.deleted_on = 0 AND online = 1 AND status = 1 AND health = 1 "
                        "AND t_proxy_ips.ip_id = %s AND ps_id = %s ORDER BY id DESC LIMIT 1 ", ip_id, ps_id)


def get_cn_proxies_by_ip(ip_addr: int) -> list:
    return db.mysql.query("""
        SELECT t_proxy_ips.id, t_proxy_ips.ip_id, INET6_NTOA(UNHEX(ip_address)) as ip_address  FROM t_proxy_ips
        LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id
        LEFT JOIN t_proxy_machines ON t_proxy_ips.pm_id = t_proxy_machines.id
        LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id
        WHERE t_proxy_ips.deleted_on = 0 AND t_proxy_suppliers.name = 'aliyun' AND CAST(CONV(ip_address, 16, 10) AS SIGNED) = %s
    """, ip_addr)


def get_cn_proxies_by_ip_fuzz(ip_addr: str) -> list:
    ip_addr = ip_addr.replace("*", "%%")
    return db.mysql.query("""
        SELECT t_proxy_ips.id, t_proxy_ips.ip_id, INET6_NTOA(UNHEX(ip_address)) as ip_address  FROM t_proxy_ips
        LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id
        LEFT JOIN t_proxy_machines ON t_proxy_ips.pm_id = t_proxy_machines.id
        LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id
        WHERE t_proxy_ips.deleted_on = 0 AND t_proxy_suppliers.name = 'aliyun' AND INET6_NTOA(UNHEX(ip_address)) LIKE %s
    """, ip_addr)


## 国内代理相关 end

def get_supplier_by_name(name: str):
    return db.mysql.get("SELECT * FROM t_proxy_suppliers WHERE deleted_on = 0 AND name = %s", name)


def get_supplier_by_id(id: int):
    return db.mysql.get(""" SELECT * FROM t_proxy_suppliers WHERE deleted_on = 0 AND id = %s """, id)


def add_user_token(uid: int, url: str, username: str, passwd: str, pi_id: int, requests: int = 0, traffic: int = 0,
                   network: str = "http", expired: bool = False, is_more_sell: bool = False, is_static: bool = False,
                   is_api: bool = False, life_time=0, luminati_account_id: int = 0, payway_id: int = 0,
                   remark: str = "", is_data_center: bool = False, ps_id: int = 0) -> int:
    t = int(time.time())
    try:
        return db.mysql.execute("""
            INSERT INTO t_user_tokens(created_on, modified_on, deleted_on, uid, url, username, passwd, pi_id, 
            requests, traffic, network, expired, is_more_sell, is_static, is_api, life_time, la_id, payway_id, remark,
            data_center, ps_id) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, t, t, 0, uid, url, username, passwd, pi_id, requests, traffic, network, expired, is_more_sell, is_static,
                                is_api, life_time, luminati_account_id, payway_id, remark, bool(is_data_center), ps_id)
    except:
        log.error(traceback.format_exc())
        return -1
def updata_token(ips_order_id,token_id):
    db.mysql.execute("update t_ip_orders set ut_id=%s where orderid=%s",token_id,ips_order_id)

def get_user_token_by_id(id: int) -> dict:
    return db.mysql.get("SELECT * FROM t_user_tokens WHERE id = %s AND deleted_on = 0", id)


def get_user_token(uid: int = None, is_static=False, is_traffic=False,
                   after_ts: int = None, before_ts: int = None, status: str = "") -> list:
    """
    得到用户3个月内生成的 token
    :param uid:         用户id
    :param is_static:   是否为静态代理， None是不区分
    :return:  token的数据
    """
    status = "" if not status or status.lower() not in ['unsupported', 'normal', 'expired',
                                                        'unhealthy'] else status.lower()
    before_ts = int(time.time()) if before_ts is None else int(before_ts)  # 默认 当前时间
    after_ts = int(time.mktime((datetime.today() + relativedelta(months=-3)).timetuple())) if after_ts is None else int(
        after_ts)  # 默认 3个月
    return db.mysql.query("""
        SELECT t_user_tokens.id, t_user_tokens.created_on, t_user_tokens.pi_id, source,
        CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, pm_id, url, t_user_tokens.is_static, la_id, traffic_usage, 
        cost, t_user_tokens.username, t_user_tokens.passwd, network, expired, online, status, health, refund, life_time,  
        -- req_usage_amount, resp_usage_amount, 
        t_geo_country.name as country_name, t_geo_country.code as country_code,
        t_geo_state.name as state_name, t_geo_state.code as state_code,
        t_geo_city.name as city_name  FROM t_user_tokens 
        LEFT JOIN t_proxy_ips ON t_user_tokens.pi_id = t_proxy_ips.id 
        LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id 
        LEFT JOIN t_geo_country ON t_proxy_ips.country_id = t_geo_country.id 
        LEFT JOIN t_geo_state ON t_proxy_ips.state_id = t_geo_state.id 
        LEFT JOIN t_geo_city ON t_proxy_ips.city_id = t_geo_city.id 
        WHERE (%s OR uid = %s) AND t_proxy_ips.deleted_on = 0  
        AND (%s OR t_user_tokens.is_static = %s) AND (%s OR ((%s AND la_id = 0) OR (%s AND la_id > 0)))
        AND t_user_tokens.created_on BETWEEN %s AND %s 
        AND ((%s AND (status = 1))  -- all
        OR (%s AND (status = 0))     -- unsupported
        OR (%s AND (status = 1 AND expired = 1))    -- expired
        OR (%s AND (status = 1 AND expired = 0 AND health = 1))     -- normal
        OR (%s AND (status = 1 AND expired = 0 AND (online = 0 OR health = 0))))    -- unhealthy
        ORDER BY t_user_tokens.created_on DESC 
    """, uid is None, uid, is_static is None, is_static, is_traffic is None, not is_traffic, is_traffic,
                          after_ts, before_ts, status == "", status == "unsupported", status == "expired",
                          status == 'normal', status == "unhealthy")


def get_history_area(uid: int, is_static: bool = False, is_traffic: bool = False) -> dict:
    """
    获取最后一条生成带来的地区
    :param uid:     用户ID
    :return:    {country_id, state_id, city_id}
    """
    return db.mysql.get("""
        SELECT network, country_id, state_id, city_id FROM t_user_tokens 
        LEFT JOIN t_proxy_ips ON t_user_tokens.pi_id = t_proxy_ips.id 
        WHERE uid = %s AND  t_user_tokens.deleted_on = 0 AND t_user_tokens.is_static = %s 
        AND (%s AND la_id > 0) AND (%s AND la_id = 0) 
        ORDER BY t_user_tokens.created_on DESC LIMIT 1
    """, uid, is_static, is_traffic, not is_traffic)

def get_record_from_pool(country_id,state_id,city_id,uid):

    if  state_id or  city_id:
        return []
    return db.mysql.get("""
select * from t_dynamic_ip_pool where country_id= %s  and  status=1  and pi_id not in (
select pi_id from t_user_tokens where uid=%s  -- 用户有这个ip
union
select pi_id from t_dynamic_ip_pool left join t_user_tokens using(pi_id) where t_dynamic_ip_pool.pi_id >0 and t_dynamic_ip_pool.status =1 group by uid having count(1) >=2  -- ip分配出去2次了
) 
 order by RAND() limit 1
    """, country_id,uid)

def update_pool_record(id,pi_id):
    db.mysql.execute("update t_dynamic_ip_pool set pi_id=%s where id=%s",pi_id,id)

def add_proxy_ips(pm_id: int = 0, forward_port: int = 0, port: int = 0, ip_id: int = 0,
                  country_id: int = 0, state_id: int = 0, city_id: int = 0,
                  online: bool = False, status: Union[bool, int] = False, health: bool = False,
                  username: str = "", password: str = "", source: str = "", db_session=None) -> int:
    """
    插入一条数据到t_proxy_ips
    :param pm_id:
    :param forward_port:
    :param port:
    :param country_id:
    :param state_id:
    :param city_id:
    :param online:
    :param status:
    :param health:
    :return:  成功：插入记录的t_proxy_ips对应的ID   失败：-1
    """
    db_mysql = db_session or db.mysql
    t = int(time.time())
    try:
        return db_mysql.execute("""
            INSERT INTO t_proxy_ips (created_on, modified_on, deleted_on, pm_id, ip_id, forward_port, port, 
            country_id, state_id, city_id, online, status, health, username, password, source) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, t, t, 0, pm_id, ip_id, forward_port, port, country_id, state_id, city_id,
                                online, status, health, username, password, source)
    except:
        log.error(traceback.format_exc())
        return -1


def get_proxy_ip(p_ip_id: int) -> dict:
    """
    得到id对应的代理ip的详细
    :param p_ip_id:
    :return:
    """
    return db.mysql.get("""
        SELECT t_proxy_ips.id, CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, 
        pm_id, ip_id, port, forward_port, username, password, online, status, health
        FROM t_proxy_ips LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id
        WHERE t_proxy_ips.id = %s AND t_proxy_ips.deleted_on = 0
    """, p_ip_id)


def change_proxy_ip_area(p_ip_id: int, country_id: int = 0, state_id: int = 0, city_id: int = 0) -> bool:
    """
    改变代理的地区
    :param p_ip_id:     t_proxy_ips.id
    :param country_id:  t_geo_country.id    (默认0：随机。前端不显示)
    :param state_id:    t_geo_state.id      (默认0：随机。前端不显示)
    :param city_id:     t_geo_city.id       (默认0：随机。前端不显示)
    :return: False:修改不成功 / True:修改成功
    """
    t = int(time.time())
    try:
        db.mysql.execute("UPDATE t_proxy_ips SET modified_on = %s, country_id = %s, state_id = %s, city_id = %s "
                         "WHERE id = %s AND deleted_on = 0", t, country_id, state_id, city_id, p_ip_id)
        return True
    except:
        log.error(traceback.format_exc())
        return False


def get_user_token_by_username_and_password(username: str, password: str) -> dict:
    return db.mysql.get("""
        SELECT uid, t_user_tokens.created_on, CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, 
        pm_id, t_user_tokens.username, t_user_tokens.passwd, network, 
        life_time, online, status, health, expired, t_user_tokens.is_static, 
        t_geo_country.name AS country_name, t_geo_country.code AS country_code,
        t_geo_state.name AS state_name, t_geo_state.code AS state_code, 
        t_geo_city.name AS city_name  FROM t_user_tokens 
        LEFT JOIN t_proxy_ips ON t_user_tokens.pi_id = t_proxy_ips.id 
        LEFT JOIN t_ips ON t_proxy_ips.ip_id = t_ips.id 
        LEFT JOIN t_geo_country ON t_proxy_ips.country_id = t_geo_country.id 
        LEFT JOIN t_geo_state ON t_proxy_ips.state_id = t_geo_state.id 
        LEFT JOIN t_geo_city ON t_proxy_ips.city_id = t_geo_city.id 
        WHERE t_user_tokens.username = %s AND passwd = %s 
        AND t_proxy_ips.deleted_on = 0 AND t_user_tokens.deleted_on = 0
    """, username, password)


def change_proxy_ips_status(p_ip_id: int, status: bool) -> bool:
    """
    更新代理的状态，即请求机器是否成功返回代理
    :param p_ip_id: t_proxy_ips的ID
    :param status:  True：成功 / False：失败
    :return:    执行sql是否成功
    """
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_proxy_ips SET modified_on = %s, status = %s WHERE id = %s AND deleted_on = 0
        """, t, status, p_ip_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def change_proxy_ips_online(p_ip_id: int, online: int) -> bool:
    """
    改变代理状态， 释放代理
    :param p_ip_id:
    :param online:
    :return:
    """
    # t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
        UPDATE t_proxy_ips SET online = %s WHERE id = %s
        """, online, p_ip_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def change_proxy_ips_health(p_ip_id: int, health: int) -> bool:
    """
    改变代理健康状态
    :param p_ip_id:
    :param health:
    :return:
    """
    # t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
        UPDATE t_proxy_ips SET health = %s WHERE id = %s
        """, health, p_ip_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def change_proxy_ips_status_online_health(p_ip_id: int, status: int, online: int, health: int) -> bool:
    try:
        effect = db.mysql.execute_rowcount("""
        UPDATE t_proxy_ips SET status = %s, online = %s, health = %s WHERE id = %s
        """, status, online, health, p_ip_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def change_proxy_ips_machine_and_port(p_ip_id: int, pm_id: int, port, forward_port, db_session=None) -> bool:
    db_mysql = db_session or db.mysql
    t = int(time.time())
    try:
        effect = db_mysql.execute_rowcount("""
            UPDATE t_proxy_ips SET modified_on = %s, pm_id = %s, port = %s, forward_port = %s 
            WHERE deleted_on = 0 AND id = %s 
        """, t, pm_id, port, forward_port, p_ip_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def get_all_machines() -> list:
    return db.mysql.query("""
        SELECT t_proxy_machines.id, ip_id, CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, domain, 
        is_linux, forward_port_start, forward_port_end, 
        t_proxy_suppliers.name as suppliers, only_http, self_check, fault  FROM t_proxy_machines 
        LEFT JOIN t_proxy_suppliers ON t_proxy_machines.ps_id = t_proxy_suppliers.id 
        LEFT JOIN t_ips ON t_proxy_machines.ip_id = t_ips.id 
        WHERE t_proxy_machines.deleted_on = 0 AND t_proxy_suppliers.deleted_on = 0 
    """)


def get_machines_by_supplier(supplier: str) -> list:
    return db.mysql.query("""
        SELECT t_proxy_machines.id, CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, 
        is_linux, forward_port_start, forward_port_end, ps_id,socks5_port,
        t_proxy_suppliers.name as suppliers, only_http, self_check, fault  FROM t_proxy_machines 
        LEFT JOIN t_proxy_suppliers ON t_proxy_machines.ps_id = t_proxy_suppliers.id 
        LEFT JOIN t_ips ON t_proxy_machines.ip_id = t_ips.id 
        WHERE t_proxy_machines.deleted_on = 0 AND t_proxy_suppliers.deleted_on = 0 AND t_proxy_suppliers.name = %s
    """, supplier)


def get_machine_by_id(pm_id: int) -> dict:
    return db.mysql.get("""
        SELECT t_proxy_machines.id AS id, CAST(CONV(ip_address, 16, 10) AS SIGNED) AS ip_addr, ps_id,socks5_port,
        forward_port_start, forward_port_end, domain FROM t_proxy_machines
        LEFT JOIN t_ips ON t_proxy_machines.ip_id = t_ips.id
        WHERE t_proxy_machines.id = %s AND t_proxy_machines.deleted_on = 0
    """, pm_id)


def get_online_machine(is_linux: bool = True) -> list:
    """
    查找已经连接上的机器的列表
    :param is_linux: 需要查找的机器是不是 Linux系统： （True:Linux/False:Windows）默认True
    :return:
    """
    return db.mysql.query("SELECT * FROM t_proxy_machines WHERE is_linux = %s AND deleted_on = 0", is_linux)


def get_online_machine_by_supplier(supplier: str) -> list:
    """
    根据supplier查找在线的机器
    :param supplier:   t_proxy_suppliers.name
    :return:
    """
    return db.mysql.query("""
        SELECT t_proxy_machines.id as id, ip_id, t_proxy_suppliers.name, is_linux, 
        forward_port_start, forward_port_end, only_http, self_check FROM t_proxy_machines 
        LEFT JOIN t_proxy_suppliers ON t_proxy_machines.ps_id = t_proxy_suppliers.id 
        WHERE t_proxy_machines.deleted_on = 0 AND t_proxy_suppliers.deleted_on = 0 
        AND t_proxy_suppliers.name = %s
    """, supplier)


def get_machines_condition(machine_ids: list) -> list:
    """
    获取机器的使用情况，已经使用了多少端口（并根据多到少排序）
    :param machine_ids:     机器ID的列表
    :return:  查询到的情况 返回 {机器ID: 已使用端口数}的列表   eg: [{1: 3}, ...]
    """
    return db.mysql.query("""
        SELECT pm_id, count(pm_id) FROM t_proxy_ips 
        WHERE online = 1 AND status = 1 AND deleted_on = 0
        AND pm_id IN %s 
        GROUP BY pm_id ORDER BY count(pm_id) DESC 
    """, (machine_ids,))


def get_one_machine_condition(pm_id: list) -> dict:
    return db.mysql.get(
        "SELECT pm_id, COUNT(1) AS total FROM t_proxy_ips WHERE deleted_on = 0 AND online = 1 AND status = 1 AND pm_id = %s",
        pm_id)


def get_machine_using_forward_port(machine_id: int) -> list:
    """
    获取每个机器当前被使用的端口
    :param machine_id:  机器的ID
    :return: 返回端口整形列表  eg: [4001, 4003, ...]
    """
    # 满足正在使用的端口的条件： 1 未过期（online = 1）；  2 当初请求开端口时返回成功（status = 1）
    using_port_dicts = db.mysql.query(
        "SELECT forward_port FROM t_proxy_ips WHERE deleted_on = 0 AND online = 1 AND status = 1 AND pm_id = %s ",
        machine_id)
    return [using_port_dict['forward_port'] for using_port_dict in using_port_dicts]


def get_machine_using_port(machine_id: int) -> list:
    """
    获取每个机器当前被使用的端口
    :param machine_id:  机器的ID
    :return: 返回端口整形列表  eg: [4001, 4003, ...]
    """
    # 满足正在使用的端口的条件： 1 未过期（online = 1）；  2 当初请求开端口时返回成功（status = 1）
    using_port_dicts = db.mysql.query(
        "SELECT port FROM t_proxy_ips WHERE deleted_on = 0 AND online = 1 AND status = 1 AND pm_id = %s ", machine_id)
    return [using_port_dict['port'] for using_port_dict in using_port_dicts]


def set_machine_fault(machine_id, fault) -> bool:
    """
    设置机器是否故障
    :param machine_id:
    :param fault:
    :return:
    """
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("UPDATE t_proxy_machines SET modified_on = %s, fault = %s "
                                           "WHERE deleted_on = 0 AND id = %s", t, fault, machine_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def insert_ip_id(proxy_ip_id: int, ip_addr: Union[int, str], db_session=None) -> bool:
    db_mysql = db_session or db.mysql
    t = int(time.time())
    try:
        ip_addr = ipaddress.ip_address(ip_addr)
        ip_addr_in_db = db_mysql.get(""" 
            SELECT * FROM t_ips WHERE CAST(CONV(ip_address, 16, 10) AS SIGNED) = %s AND deleted_on = 0 
        """, int(ip_addr))
        if ip_addr_in_db:
            ip_id = ip_addr_in_db['id']
        else:
            ip_id = db_mysql.execute("""
                INSERT INTO t_ips (ip_addr, ip_address, created_on, modified_on, deleted_on ) 
                VALUES (%s, HEX(INET6_ATON(%s)), %s, %s, %s)
            """, int(ip_addr) if type(ip_addr) == ipaddress.IPv4Address else 0, str(ip_addr), t, t, 0)
        db_mysql.execute("""
            UPDATE t_proxy_ips SET modified_on = %s, ip_id = %s, online = 1, health = 1 WHERE id = %s AND deleted_on = 0 
        """, t, ip_id, proxy_ip_id)
        return True
    except:
        log.error(traceback.print_exc())
        return False


def get_user_new_proxy_amount(uid: int) -> int:
    counter = db.mysql.get("SELECT COUNT(*) FROM t_user_tokens WHERE deleted_on = 0 AND uid = %s", uid)
    return counter['COUNT(*)']


def get_same_ip(ip_addr: int) -> dict:
    return db.mysql.get("""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_address, 
        t_proxy_suppliers.name AS suppliers FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id 
        LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id 
        WHERE CAST(CONV(ip_address, 16, 10) AS SIGNED) = %s AND t_proxy_ips.deleted_on = 0 
        AND online = 1 AND status = 1 AND health = 1 
        AND t_proxy_suppliers.name = '911' ORDER BY t_proxy_ips.created_on DESC LIMIT 1
    """, ip_addr)


#### 二次911相关  start #####


def get_sec911_proxy_by_area(country_id: int = 0, state_id: int = 0, city_id: int = 0, uid: int = 0) -> list:
    nest_sql = ""
    if country_id:
        nest_sql += f" AND country_id = {country_id} "
        if state_id:
            nest_sql += f" AND state_id = {state_id} "
            if city_id:
                nest_sql += f" AND city_id = {city_id} "
    if uid:
        nest_sql += f" AND  t_proxy_ips.id not in (select pi_id from t_user_tokens where uid= {uid}" \
                    f" and deleted_on = 0 and is_static=0 ) "
    log.info(f"""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_address, 
        t_proxy_suppliers.name as suppliers FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id 
        LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id 
        WHERE online = 1 AND status = 1 AND health = 1 AND t_proxy_ips.deleted_on = 0 
        AND t_proxy_suppliers.name = 'adspower' {nest_sql}
    """)
    return db.mysql.query(f"""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_address, 
        t_proxy_suppliers.name as suppliers FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id 
        LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id 
        WHERE online = 1 AND status = 1 AND health = 1 AND t_proxy_ips.deleted_on = 0 
        AND t_proxy_suppliers.name = 'adspower' {nest_sql}
    """)


def get_sec911_proxy_by_ip(_ip: str) -> list:
    _ip = _ip.replace("*", "%%")
    return db.mysql.query("""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_address, 
        t_proxy_suppliers.name as suppliers FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id 
        LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id 
        WHERE online = 1 AND status = 1 AND health = 1 AND t_proxy_ips.deleted_on = 0 
        AND t_proxy_suppliers.name = 'adspower' AND INET6_NTOA(UNHEX(ip_address)) LIKE %s
    """, _ip)


##### 二次911相关  end  #####


def change_911_balance(machine_id, value) -> bool:
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_911_accounts SET balance = balance + %s WHERE deleted_on = 0 AND pm_id = %s AND balance > 0
        """, value, machine_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def get_first_using_proxy():
    cover_setting = db.mysql.get("SELECT * FROM t_sell_settings WHERE deleted_on = 0 AND type = 'cover'")
    if cover_setting and cover_setting['on_off']:
        now_ts, second = int(time.time()), cover_setting['how_long'] * 60 * 60
        return db.mysql.get("""
            SELECT  t_proxy_ips.id, pm_id, forward_port FROM t_proxy_ips 
            LEFT JOIN t_proxy_machines ON t_proxy_machines.id = t_proxy_ips.pm_id 
            LEFT JOIN t_proxy_suppliers ON t_proxy_suppliers.id = t_proxy_machines.ps_id
            WHERE fault = 0 AND name = '911' AND t_proxy_ips.deleted_on = 0 AND status = 1 AND online = 1 
            AND forward_port BETWEEN forward_port_start AND forward_port_end
            AND t_proxy_ips.created_on <= %s ORDER BY t_proxy_ips.created_on LIMIT 1
        """, now_ts - second)
    else:
        return None


##### 静态IP #####

def get_user_using_static_proxies(uid) -> list:
    return db.mysql.query("""
        SELECT t_user_tokens.id, pi_id, ip_id FROM t_user_tokens LEFT JOIN t_proxy_ips ON t_proxy_ips.id = t_user_tokens.pi_id 
        WHERE t_user_tokens.deleted_on = 0 AND uid = %s AND t_user_tokens.is_static = 1 AND expired = 0
    """, uid)


def get_not_expired_tokens_by_proxy_id(pi_id) -> list:
    return db.mysql.query("""
        SELECT * FROM t_user_tokens WHERE pi_id = %s AND deleted_on = 0 AND expired = 0 
    """, pi_id)


def search_static_proxies(offset: int, limit: int, ip_addr: str = "", country_id: int = 0, state_id: int = 0,
                          city_id: int = 0,
                          without_pi_ids=None):
    without_pi_ids = list(without_pi_ids) if without_pi_ids else []  # 如果without为None就设置为空数组
    # 排除掉每个代理使用人数超过设定值的代理
    setting = db.mysql.get(""" SELECT * FROM t_sell_settings WHERE deleted_on = 0 AND type = 'static_proxy' """)
    how_many = setting['how_many'] if setting else 9999
    using_too_more_pi_ids = [i['pi_id'] for i in db.mysql.query(""" 
    SELECT pi_id, COUNT(1) AS sum FROM t_user_tokens 
    WHERE deleted_on = 0 AND t_user_tokens.is_static = 1 AND expired = 0 GROUP BY pi_id HAVING sum >= %s
    """, how_many)]
    without_pi_ids.extend(using_too_more_pi_ids)
    sql = """
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_addr, pm_id, 
        t_geo_country.name AS country_name, t_geo_country.code AS country_code, 
        t_geo_state.name AS state_name, t_geo_state.code AS state_code, 
        t_geo_city.name AS city_name FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_geo_country ON t_proxy_ips.country_id = t_geo_country.id 
        LEFT JOIN t_geo_state ON t_proxy_ips.state_id = t_geo_state.id 
        LEFT JOIN t_geo_city ON t_proxy_ips.city_id = t_geo_city.id 
        WHERE t_proxy_ips.deleted_on = 0 AND online = 1 AND status = 1 AND health = 1 AND port = 0
    """
    counter_sql = """ 
        SELECT COUNT(1), INET6_NTOA(UNHEX(ip_address)) as ip_addr FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id  
        WHERE t_proxy_ips.deleted_on = 0 AND online = 1 AND status = 1 AND health = 1 AND port = 0
    """
    if ip_addr:
        _ip = ip_addr.replace("*", "%%")
        sql += f" AND INET6_NTOA(UNHEX(ip_address)) LIKE '{_ip}' "
        counter_sql += f" AND INET6_NTOA(UNHEX(ip_address)) LIKE '{_ip}' "
    if country_id:
        sql += f" AND t_proxy_ips.country_id = {country_id} "
        counter_sql += f" AND t_proxy_ips.country_id = {country_id} "
    if state_id:
        sql += f" AND t_proxy_ips.state_id = {state_id} "
        counter_sql += f" AND t_proxy_ips.state_id = {state_id} "
    if city_id:
        sql += f" AND t_proxy_ips.city_id = {city_id} "
        counter_sql += f" AND t_proxy_ips.city_id = {city_id} "
    if without_pi_ids:
        sql += f" AND t_proxy_ips.id NOT IN ({','.join([str(pi_id) for pi_id in without_pi_ids])}) "
        counter_sql += f" AND t_proxy_ips.id NOT IN ({','.join([str(pi_id) for pi_id in without_pi_ids])}) "
    sql += f" LIMIT {limit} OFFSET {offset}"
    # print(sql)
    return db.mysql.get(counter_sql)['COUNT(1)'], db.mysql.query(sql)


def get_static_proxies_total(without_pi_ids=None) -> int:
    without_pi_ids = list(without_pi_ids) if without_pi_ids else []  # 如果without为None就设置为空数组
    return db.mysql.get(f"""
        SELECT COUNT(1) FROM t_proxy_ips WHERE deleted_on = 0 AND online = 1 AND status = 1 AND health = 1 AND port = 0
        {f"AND t_proxy_ips.id NOT IN ({','.join([str(id) for id in without_pi_ids])})" if without_pi_ids else ""}
    """)["COUNT(1)"]


def get_all_static_proxies_paging(offset: int, limit: int, without_pi_ids=None) -> list:
    without_pi_ids = list(without_pi_ids) if without_pi_ids else []  # 如果without为None就设置为空数组
    sql = f"""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_addr, pm_id, 
        t_geo_country.name AS country_name, t_geo_country.code AS country_code, 
        t_geo_state.name AS state_name, t_geo_state.code as state_code, 
        t_geo_city.name AS city_name FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_geo_country ON t_proxy_ips.country_id = t_geo_country.id 
        LEFT JOIN t_geo_state ON t_proxy_ips.state_id = t_geo_state.id 
        LEFT JOIN t_geo_city ON t_proxy_ips.city_id = t_geo_city.id 
        WHERE t_proxy_ips.deleted_on = 0 AND online = 1 AND status = 1 AND health = 1 AND port = 0
        {f"AND t_proxy_ips.id NOT IN ({','.join([str(id) for id in without_pi_ids])})" if without_pi_ids else ""}
        GROUP BY RAND() LIMIT %s OFFSET %s
    """
    proxies = db.mysql.query(sql, offset, limit)
    return proxies


def get_static_proxies(ip_addr: str = "", country_id: int = 0, state_id: int = 0, city_id: int = 0,
                       is_socks: bool = False,
                       without_pi_ids=None) -> list:
    without_pi_ids = list(without_pi_ids) if without_pi_ids else []  # 如果without为None就设置为空数组
    sql = f"""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_addr, pm_id, 
        t_geo_country.name AS country_name, t_geo_country.code AS country_code, 
        t_geo_state.name AS state_name, t_geo_state.code AS state_code, 
        t_geo_city.name as city_name FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_geo_country ON t_proxy_ips.country_id = t_geo_country.id 
        LEFT JOIN t_geo_state ON t_proxy_ips.state_id = t_geo_state.id 
        LEFT JOIN t_geo_city ON t_proxy_ips.city_id = t_geo_city.id 
        LEFT JOIN t_proxy_machines ON t_proxy_ips.pm_id = t_proxy_machines.id
        LEFT JOIN t_proxy_suppliers on t_proxy_machines.ps_id = t_proxy_suppliers.id
        WHERE t_proxy_ips.deleted_on = 0 AND t_proxy_ips.country_id > 0 
        AND online = 1 AND status = 1 AND health = 1 AND port = 0
        {f"AND INET_NTOA(ip_addr) LIKE '{ip_addr.replace('*', '%%')}'" if ip_addr else ""}
        {f"AND t_proxy_ips.country_id = {country_id}" if country_id else ""}
        {f"AND t_proxy_ips.state_id = {state_id}" if state_id else ""}
        {f"AND t_proxy_ips.city_id = {city_id}" if city_id else ""}
        {f"AND t_proxy_suppliers.only_http = 0" if is_socks else ""}
        {f"AND t_proxy_ips.id NOT IN ({','.join([str(id) for id in without_pi_ids])})" if without_pi_ids else ""}
    """
    # print(sql)
    return db.mysql.query(sql)


def get_static_proxies_by_ip(ip_addr: str, is_socks: bool = False, without_pi_ids=None) -> list:
    without_pi_ids = list(without_pi_ids) if without_pi_ids else []  # 如果without为None就设置为空数组
    sql = f"""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) AS ip_addr, pm_id, 
        t_geo_country.name as country_name, t_geo_country.code as country_code, 
        t_geo_state.name as state_name, t_geo_state.code as state_code, 
        t_geo_city.name as city_name FROM t_proxy_ips
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_geo_country ON t_proxy_ips.country_id = t_geo_country.id 
        LEFT JOIN t_geo_state ON t_proxy_ips.state_id = t_geo_state.id 
        LEFT JOIN t_geo_city ON t_proxy_ips.city_id = t_geo_city.id 
        LEFT JOIN t_proxy_machines ON t_proxy_ips.pm_id = t_proxy_machines.id
        LEFT JOIN t_proxy_suppliers on t_proxy_machines.ps_id = t_proxy_suppliers.id
        WHERE t_proxy_ips.deleted_on = 0 AND t_proxy_ips.country_id > 0 
        AND online = 1 AND status = 1 AND health = 1 AND port = 0 AND t_proxy_ips.ip_id != 0
        AND t_proxy_suppliers.name IN ('supplier1_http', 'supplier1_socks5')
        {f"AND INET_NTOA(ip_addr) LIKE '{ip_addr.replace('*', '%%')}'" if ip_addr else ""}
        {f"AND t_proxy_suppliers.only_http = 0" if is_socks else ""}
        {f"AND t_proxy_ips.id NOT IN ({','.join([str(id) for id in without_pi_ids])})" if without_pi_ids else ""}
    """
    # print(sql)
    return db.mysql.query(sql)


def get_static_proxies_by_area(country_id: int = 0, state_id: int = 0, city_id: int = 0, is_socks: bool = False,
                               without_pi_ids=None) -> list:
    without_pi_ids = list(without_pi_ids) if without_pi_ids else []  # 如果without为None就设置为空数组
    sql = f"""
        SELECT t_proxy_ips.id, INET6_NTOA(UNHEX(ip_address)) as ip_addr, t_proxy_ips.ip_id, pm_id, 
        t_geo_country.name as country_name, t_geo_country.code as country_code, 
        t_geo_state.name as state_name, t_geo_state.code as state_code, 
        t_geo_city.name as city_name FROM t_proxy_ips 
        LEFT JOIN t_ips ON t_ips.id = t_proxy_ips.ip_id 
        LEFT JOIN t_geo_country ON t_proxy_ips.country_id = t_geo_country.id 
        LEFT JOIN t_geo_state ON t_proxy_ips.state_id = t_geo_state.id 
        LEFT JOIN t_geo_city ON t_proxy_ips.city_id = t_geo_city.id 
        LEFT JOIN t_proxy_machines ON t_proxy_ips.pm_id = t_proxy_machines.id
        LEFT JOIN t_proxy_suppliers on t_proxy_machines.ps_id = t_proxy_suppliers.id
        WHERE t_proxy_ips.deleted_on = 0 AND t_proxy_ips.ip_id != 0 
        AND online = 1 AND status = 1 AND health = 1 AND port = 0
        AND t_proxy_suppliers.name IN ('supplier1_http', 'supplier1_socks5')
        {f"AND t_proxy_ips.country_id = {country_id}" if country_id else "AND t_proxy_ips.country_id > 0"}
        {f"AND t_proxy_ips.state_id = {state_id}" if state_id else ""}
        {f"AND t_proxy_ips.city_id = {city_id}" if city_id else ""}
        {f"AND t_proxy_suppliers.only_http = 0" if is_socks else ""}
        {f"AND t_proxy_ips.id NOT IN ({','.join([str(id) for id in without_pi_ids])})" if without_pi_ids else ""}
    """
    # print(sql)
    return db.mysql.query(sql)


def change_user_token_life_time(token_id, life_time) -> bool:
    try:
        effect = db.mysql.execute_rowcount(""" 
            UPDATE t_user_tokens SET life_time = life_time + %s WHERE id = %s AND deleted_on = 0 AND expired = 0
        """, life_time, token_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


def change_user_token_created_on_and_expired_and_life_time(token_id, expired=0, life_time=0) -> bool:
    ts = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_user_tokens SET created_on = %s, expired = %s, life_time = %s WHERE deleted_on = 0 AND id = %s
        """, ts, expired, life_time, token_id)
        return effect > 0
    except:
        log.error(traceback.format_exc())
        return False


# 流量代理相关
def get_luminati_account(only_country: bool = False, is_static: bool = False, is_data_center: bool = False,
                         is_oxylabs: bool = False,type = 0,s=2,is_api=False):
    if is_oxylabs:
        type = 1
        only_country = False
    if is_api:
        if s == 2 and type != 3:
            type = 0
        elif s == 1:
            type = 1
        elif s == 3:
            import random
            type = [2,4]
            type = random.choice(type)
        else:
            type = 3

    return db.mysql.get("""
        SELECT * FROM t_luminati_accounts WHERE deleted_on = 0  AND only_country = %s  and type = %s
        AND is_static = %s AND is_data_center = %s ORDER BY RAND() LIMIT 1
    """, only_country,type, is_static, is_data_center)


def get_luminati_accounts(only_country: bool = False, is_static: bool = False, is_data_center: bool = False,
                          is_oxylabs: bool = False,ids:list = list()):
    params = [is_oxylabs, only_country, is_static, is_data_center]
    if ids:
        if len(ids)>1:
            params.append( tuple(ids))
            extra_sql = " AND id IN %s"
        else:
            params.append(ids[0])
            extra_sql = " AND id = %s"
    else:
        extra_sql = "and has_special_area = False "
    log.error("""
        SELECT * FROM t_luminati_accounts WHERE deleted_on = 0 AND is_oxylabs = %s AND only_country = %s 
        AND is_static = %s AND is_data_center = %s 
    """ + extra_sql, *params )

    return db.mysql.query("""
        SELECT * FROM t_luminati_accounts WHERE deleted_on = 0 AND is_oxylabs = %s AND only_country = %s 
        AND is_static = %s AND is_data_center = %s  and type = 0 
    """ + extra_sql, *params )



def insert_traffic_ip(ti_id,pi_id,uid):

    q = db.mysql.get("select id from t_user_traffic_ips where ti_id=%s  and uid=%s limit 1",ti_id,uid)
    if q:
        db.mysql.execute("delete from  t_user_traffic_ips  where ti_id=%s  and uid=%s ",ti_id,uid)
    sql = """
        insert into t_user_traffic_ips (ti_id,pi_id,uid,created_on) values (%s,%s,%s,unix_timestamp())
    """
    return db.mysql.execute_rowcount(sql,ti_id,pi_id,uid)


def get_t_proxys(country_id:int=0,uid:int=0,is_data_center:bool=False,**kwargs):
    def add_where(condition,key="country_id"):
        if condition > 0:
            str = f"and {key}=%s"
        else:
            str = f"and {key}>=%s"
        return str

    state_id = kwargs.get("state_id",0)
    city_id = kwargs.get("city_id",0)
    network_type = kwargs.get("network_type","http")
    country = add_where(country_id,"country_id")
    state = add_where(state_id,"state_id")
    city = add_where(city_id,"city_id")
    delete_on = int(datetime.now().timestamp()-60*60*24)
    extra = ""
    if not is_data_center:
        if kwargs.get("s") == 1:
            extra = " and ti.la_id != 99"
        elif kwargs.get("s") == 2:
            extra = " and ti.la_id = 99"

            

    sql =f"""
        select ti.id as ti_id,check_proxy,ip,ip_int,ip_hex,_username as  username,_password as password,country_id,pm_id,ti.la_id,_port as forward_port,_host as host from 
        t_traffic_ips  ti left join 
         t_user_traffic_ips tu on ti.id=tu.ti_id  and tu.uid=%s
         join t_luminati_accounts la on la.id=la_id left join t_proxy_machines pm on pm.id=pm_id
        where   (tu.ti_id is null or (tu.deleted_on>0 and tu.deleted_on<{delete_on})) and is_data_center=%s and is_static=1 {country} {state} {city} {extra} and ti.is_health=1
        and network_type=%s
        and ti.deleted_on = 0 
        order by ti.la_id desc,ti.id asc  
          limit 2
    """
    records = db.mysql.query(sql,uid,is_data_center,country_id,state_id,city_id,network_type)

    # check
    if records:
        for record in records:
            is_oracle_proxy = record.get("pm_id") in (18280,)
            if is_oracle_proxy:
                record["username"] = record.get("username").split("-")[0]

            # res = requests.get("http://lumtest.com/myip.json",proxies={"http":record.check_proxy,"https":record.check_proxy,})
            # if res.status_code == 200:
                # 获取ip_id
            ip_id = db.mysql.get("select id from t_ips where ip_addr=%s",record['ip_int'])
            if not ip_id:
                ip_id = db.mysql.execute("insert into t_ips (ip_addr,ip_address,created_on) values (%s,%s,unix_timestamp())",record['ip_int'],record['ip_hex'][2:].upper())
            else:
                ip_id = ip_id['id']
            if is_oracle_proxy:
                pm = db.mysql.get("select id from t_proxy_machines where domain=%s ",record.get("ip") )

                if not pm:
                    now = int(time.time())
                    pm_id = db.mysql.execute("INSERT INTO t_proxy_machines ( `ip_id`, `domain`, `ps_id`, `created_on`, "
                                          "`modified_on`, `is_linux`, `forward_port_start`, `forward_port_end`, `fault`, `public_ip`, `vm_name`) VALUES ( %s, %s, 13, %s, %s,  1, %s, %s, 0, '', '');",0,record.get("host"),now,now,record.get("forward_port"),record.get("forward_port"))
                else:
                    pm_id = pm["id"]
                record["pm_id"] = pm_id
            return record,ip_id
            # else:
            #     db.mysql.execute("update t_traffic_ips set is_health=0,last_check_time=unix_timestamp() where id=%s",record['ti_id'])
            #     log.error(f"check proxy error {record.check_proxy}")
    else:
        db.mysql.execute("insert into t_traffic_log (country_id,state_id,city_id,created_on,uid,is_static,is_datacenter) values (%s,%s,%s,unix_timestamp(),%s,1,%s)",country_id,state_id,city_id,uid,is_data_center)
        log.error(f"no proxy for country {country_id}")


def get_user_laid_ratio(uid: int, laid_in: tuple, desc: bool = True) -> list:
    return db.mysql.query(f"""-- sql
        SELECT la_id, COUNT(1) AS num FROM t_user_tokens 
        WHERE uid = %s AND deleted_on = 0 AND is_deleted = 0 AND la_id IN %s 
        GROUP BY la_id ORDER BY num {"DESC" if desc else ''}
    """, uid, laid_in)


def set_token_remark(uid: int, token_id: int, content: str = "") -> bool:
    """
    修改代理备注
    :param uid:
    :param token_id:
    :param content:
    :return:
    """
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_user_tokens SET remark = %s WHERE deleted_on = 0 AND id = %s AND uid = %s 
        """, content, token_id, uid)
        return effect > 0
    except:
        traceback.print_exc()
        return False


def get_user_using_static_traffic_proxies(uid: int):
    return db.mysql.query("""
        SELECT t_user_tokens.id, pi_id, ip_id FROM t_user_tokens 
        LEFT JOIN t_proxy_ips ON t_proxy_ips.id = t_user_tokens.pi_id 
        WHERE t_user_tokens.deleted_on = 0 AND uid = %s AND t_user_tokens.is_static = 1 
        AND expired = 0 AND t_user_tokens.la_id > 0
    """, uid)


def is_ip_id_in_user(uid, ip_id):
    return db.mysql.get("""
        SELECT COUNT(1) as c FROM t_user_tokens 
        LEFT JOIN t_proxy_ips on t_user_tokens.pi_id = t_proxy_ips.id
        WHERE ip_id = %s AND uid = %s
        AND t_proxy_ips.deleted_on = 0 AND t_user_tokens.deleted_on = 0
    """, ip_id, uid)['c'] > 0


def get_azure_location_by_id(_id: int) -> Optional[dict]:
    return db.mysql.get("""
        SELECT * FROM t_azure_locations WHERE deleted_on = 0 AND id = %s 
    """, _id)


def get_lief_1_list(uid):
    sql = "select how_many from t_sell_settings where type='buy_residential_private_static_ip_1days' and on_off=1"
    obj = db.mysql.get(sql)
    if obj:
        pids = db.mysql.query("select pi_id from t_user_tokens where la_id=0 "
                              "and expired=0 and is_deleted=0 and is_static=1 and life_time=1 and pi_id not in (select pi_id from t_user_tokens where uid=%s and "
                              "la_id=0 and expired=0 and is_deleted=0 and is_static=1)"
                              " group by pi_id HAVING count(distinct uid) <%s  order by count(distinct uid) desc",uid, obj["how_many"])
        return [pi_id.pi_id for pi_id in pids]


## 静态私有代理
@timer
def choose_static_private_ips(uid, suppliers_name: Union[list, tuple], exclude_ids=None,
                              num: int = 1, country_id: int = 0, state_id: int = 0, city_id: int = 0,**kwargs):
    exclude_ids = exclude_ids or (0,)
    in_list = kwargs.get("in_list", [])
    only_socks = kwargs.get("only_socks")

    if kwargs.get("is_one_days"):
        rows = db.mysql.query(f"""-- sql
                SELECT tpi.id, tpi.username, tpi.password, tpi.forward_port, ti.ip_addr, ti.ip_address, tpm.domain, tpm.ps_id,
                only_http,only_socks, is_data_center, tpi.created_on FROM t_proxy_ips tpi 
                LEFT JOIN t_user_tokens tut on tpi.id = tut.pi_id AND tut.deleted_on = 0 AND tut.is_static = 1 AND la_id = 0 AND (tut.uid = %s OR expired = 0)
                LEFT JOIN t_proxy_machines tpm on tpi.pm_id = tpm.id
                LEFT JOIN t_proxy_suppliers tps on tps.id = tpm.ps_id
                LEFT JOIN t_ips ti ON ti.id = tpm.ip_id
                WHERE tpi.deleted_on = 0 AND status = 1 AND health = 1 AND online = 1 {"and only_socks=1" if only_socks else "and only_socks=0" } AND {f"tut.id IS NULL" if not kwargs.get("is_one_days") else "tut.life_time=1"}
                {f"AND tpi.country_id = {country_id}" if country_id else "AND tpi.country_id > 0"}
                {f"AND tpi.state_id = {state_id}" if state_id else ""}
                {f"AND tpi.city_id = {city_id}" if city_id else ""}
                {f"AND tpi.id in {in_list}" if in_list else ""}
                and tut.pi_id not in (select pi_id from t_user_tokens where uid={uid} and is_static=1 and la_id=0)
                AND source in %s AND tpi.id not in %s 
                order by FIELD(source,{",".join([f"'{i}'" for i in suppliers_name])}) LIMIT %s
            """, uid, suppliers_name, exclude_ids, num)

        if rows:
            return rows


    rows =  db.mysql.query(f"""-- sql
        SELECT tpi.id, tpi.username, tpi.password, tpi.forward_port, ti.ip_addr, ti.ip_address, tpm.domain, tpm.ps_id,
        only_http,only_socks, is_data_center, tpi.created_on FROM t_proxy_ips tpi 
        LEFT JOIN t_user_tokens tut on tpi.id = tut.pi_id AND tut.deleted_on = 0 AND tut.is_static = 1 AND la_id = 0 AND (tut.uid = %s OR expired = 0)
        LEFT JOIN t_proxy_machines tpm on tpi.pm_id = tpm.id
        LEFT JOIN t_proxy_suppliers tps on tps.id = tpm.ps_id
        LEFT JOIN t_ips ti ON ti.id = tpm.ip_id
        WHERE tpi.deleted_on = 0 AND status = 1 AND health = 1 AND online = 1 AND tut.id IS NULL
        {f"AND tpi.country_id = {country_id}" if country_id else "AND tpi.country_id > 0"}
        {f"AND tpi.state_id = {state_id}" if state_id else ""}
        {f"AND tpi.city_id = {city_id}" if city_id else ""}
        AND source in %s AND tpi.id not in %s 
        ORDER BY FIELD(source,{",".join([f"'{i}'" for i in suppliers_name])}) LIMIT %s
    """, uid,suppliers_name, exclude_ids, num )
    log.error(f"choose_static_private_ips2 {rows}")
    return rows

def re_new_ip(uid,token_id,pay_way_id,is_auto_renew,t_ip_order_id,execute_user):
    obj = db.mysql.get("select is_auto_renew,pay_way_id from t_static_ip_renew where created_by=-1 and token_id=%s order by id desc limit 1",token_id)
    if obj:
        if obj['is_auto_renew'] == is_auto_renew and obj['pay_way_id'] == pay_way_id:
            pass
        else:
            db.mysql.execute("update t_static_ip_renew set is_auto_renew=%s,pay_way_id=%s where created_by=-1 and token_id=%s",is_auto_renew,pay_way_id,token_id)
    db.mysql.execute("""
            insert into t_static_ip_renew (order_id,uid,token_id,t_ip_order_id,created_on,is_auto_renew,created_by,pay_way_id) values
            (%s,%s,%s,%s,%s,%s,%s,%s);
            """, *(
    0, uid, token_id, t_ip_order_id, int(time.time()), is_auto_renew, execute_user, pay_way_id))


def get_static_private_ip_by_token_id(token_id: int, uid: int):
    try:
        return db.mysql.get("""
            SELECT expired, t_user_tokens.created_on, life_time, health, online, status, pi_id, url, network, ps_id,
            t_user_tokens.username, t_user_tokens.passwd FROM t_user_tokens 
            LEFT JOIN t_proxy_ips ON pi_id = t_proxy_ips.id
            WHERE t_user_tokens.deleted_on = 0 AND t_proxy_ips.deleted_on = 0 
            AND t_user_tokens.id = %s AND uid = %s
        """, token_id, uid)
    except:
        return None


def get_proxy_using_count(pi_id: int, not_in_uid=None) -> int:
    not_in_uid = not_in_uid or (0,)
    return db.mysql.get("""
        SELECT COUNT(1) as num FROM t_user_tokens WHERE deleted_on = 0 AND expired = 0
        AND pi_id = %s AND uid not in %s 
    """, pi_id, not_in_uid)['num']


def delete_token_by_id(token_id: int) -> bool:
    t = int(time.time())
    try:
        effect = db.mysql.execute_rowcount("""
            UPDATE t_user_tokens SET deleted_on = %s WHERE deleted_on = 0 AND id = %s  
        """, t, token_id)
        return effect > 0
    except:
        return False


def get_proxy_obj_by_id(pi_id: int):
    try:
        return db.mysql.get("""
            SELECT tpi.id, tpi.username, tpi.password, tpi.forward_port, 
            ti.ip_addr, ti.ip_address, tpm.domain FROM t_proxy_ips tpi 
            LEFT JOIN t_proxy_machines tpm on tpi.pm_id = tpm.id
            LEFT JOIN t_proxy_suppliers tps on tps.id = tpm.ps_id
            LEFT JOIN t_ips ti ON ti.id = tpm.ip_id
            WHERE tpi.deleted_on = 0 AND tpi.id = %s
        """, pi_id)
    except:
        return None


def get_iproyal_location_by_country_id(country_id: int):
    try:
        return db.mysql.get("""
            SELECT location_id, ceiling FROM t_iproyal_loacations WHERE deleted_on = 0 AND country_id = %s
        """, country_id)
    except:
        return None


def save_iproyal_order(iproyal_order_id: int, location_id: int, pi_id: int, pay_order_id: int = 0, status: int = 0
                       ) -> bool:
    t = int(time.time())
    try:
        db.mysql.execute("""
            INSERT INTO t_iproyal_orders (created_on, modified_on, deleted_on, 
            order_id, status, location_id, pi_id, pay_order_id) VALUES 
            (%s, %s, %s, %s, %s, %s, %s, %s);
        """, t, t, 0, iproyal_order_id, status, location_id, pi_id, pay_order_id)
        return True
    except:
        traceback.print_exc()
        return False


def get_iproyal_order_by_pi_id(pi_id):
    res = db.mysql.query("""
        SELECT * FROM t_iproyal_orders WHERE deleted_on = 0 AND  pi_id = %s
    """, pi_id)
    return res[0] if res else None


def get_proxy_num(country_id: int, source_in):
    try:
        return db.mysql.get("""
            SELECT COUNT(1) AS num FROM t_proxy_ips WHERE source IN %s AND deleted_on = 0 AND status in (1, 2, 3)
            AND country_id = %s AND health = 1 AND online = 1
        """, source_in, country_id)['num']
    except Exception:
        traceback.print_exc()
        return None


def get_ipaddr_by_pid(uid, pi_id) -> str:
    try:
        results = db.mysql.query("""
            SELECT ti.ip_addr FROM t_proxy_ips tpi 
            LEFT JOIN t_user_tokens tut on tpi.id = tut.pi_id AND tut.deleted_on = 0 AND tut.is_static = 1 AND la_id = 0 AND (tut.uid = %s OR expired = 0)
            LEFT JOIN t_proxy_machines tpm on tpi.pm_id = tpm.id
            LEFT JOIN t_proxy_suppliers tps on tps.id = tpm.ps_id
            LEFT JOIN t_ips ti ON ti.id = tpi.ip_id
            WHERE tpi.deleted_on = 0 AND tpi.id = %s
        """, uid, pi_id)
        ip_addr = results[0].get("ip_addr")

        return socket.inet_ntoa(int(ip_addr).to_bytes(4, 'big'))
    except Exception:
        return ""
