#/usr/bin/env python
# -*- coding:utf-8 -*-
import queue
# Copyright 2020

# @author: Jerry<PERSON><PERSON>
import string
import random
import threading
import time

import yaml
import os.path
import logging
from celery import Celery

from libs import torndb


class YamlLoader(yaml.Loader):
    """ Yaml loader

    Add some extra command to yaml.

    !include:
        see http://stackoverflow.com/questions/528281/how-can-i-include-an-yaml-file-inside-another
        include another yaml file into current yaml
    """
    def __init__(self, stream):
        self._root = os.path.split(stream.name)[0]
        super(YamlLoader, self).__init__(stream)

    def include(self, node):
        filename = os.path.join(self._root, self.construct_scalar(node))
        with open(filename, 'r') as f:
            return yaml.load(f, YamlLoader)


YamlLoader.add_constructor('!include', YamlLoader.include)


class Loggers(object):
    """简单的logging wrapper"""

    def __init__(self):
        self.loggers = {}

    def use(self, log_name, log_path):
        if not log_name in self.loggers:
            logger = logging.getLogger(log_name)
            logger.setLevel(logging.INFO)
            if not logger.handlers:
                fh = logging.FileHandler(log_path)
                fh.setLevel(logging.INFO)
                formatter = logging.Formatter('[%(asctime)s] - %(message)s')
                fh.setFormatter(formatter)
                logger.addHandler(fh)
            self.loggers[log_name] = logger
        return self.loggers[log_name]


loggers = Loggers()


def pagination(total_count, page, limit=40):
    """分页器

    data: 分页数据,
    page: 指定页码,
    limit: 每页长度,
    """
    total_page = (total_count - 1) / limit + 1 if total_count > 0 else 0
    page = total_count / limit if page * limit > total_count else page - 1
    pre_page = page if page > 0 else None
    next_page = page + 2 if page * limit < total_count else None
    offset = page * limit
    result = {
        "total_page": total_page,
        "cur_page": page + 1,
        "pre_page": pre_page,
        "next_page": next_page,
        "offset":offset,
        "limit": limit
    }
    return result


def randomString(length: int, number: bool=True, uppercase: bool=True, lowercase: bool=True) -> str:
    if type(length) != int:
        raise TypeError("length must be int")
    scope = ""
    if number :
        scope += string.digits
    if uppercase:
        scope += string.ascii_uppercase
    if lowercase:
        scope += string.ascii_lowercase
    if scope:
        return ''.join(random.choice(scope) for _ in range(length))
    else:
        raise ValueError("number / uppercase / lowercase not all False")


