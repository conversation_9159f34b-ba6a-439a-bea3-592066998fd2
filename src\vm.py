import json

import _thread
from libs.az import AZController
import constants
from src.base import ResponseBase
from models.proxy import *
from models.ip_order import *
from models.vm import *
from utils.functions import languageChecker


class VM(ResponseBase):

    def __init__(self, uid:int=None, lang:str='us'):
        self.uid, self.lang = uid, languageChecker(lang)
        self.az = AZController(group=constants.WIN_VM['group'], vm_name_prefix=constants.WIN_VM['vm_name_prefix'],
                               image=constants.WIN_VM['image'])

    def vms(self):
        vm_tokens = get_vm_tokens(self.uid)
        return self.returnSucceed({'vm_tokens': [self._wapperVMToken(vm_token) for vm_token in vm_tokens]})

    def _wapperVMToken(self, item):
        status = "running"
        if item['status'] == 0:
            status = "creating"
        elif item['status'] == 2:
            status = "failed"
        elif db.redis.get(f"starting_mv_pi_id_{item['pi_id']}"):
            status = "starting"
        elif db.redis.get(f"stopping_mv_pi_id_{item['pi_id']}"):
            status = "stopping"
        elif db.redis.get(f"restarting_mv_pi_id_{item['pi_id']}"):
            status = "restarting"
        elif item['expired'] == 1:
            status = "expired"
        elif item['online'] == 0:
            status = "outline"
        elif item['health'] == 0:
            status = "stop"
        res_data = {
            'id': item['id'],
            'ip_address': str(ipaddress.ip_address(item['ip_addr'])) if item['ip_addr'] else "",
            'username': item['username'],
            'password': item['passwd'],
            'created_on': item['created_on'],
            'life_time': item['life_time'],
            'remark': item['remark'],
            'status': status,
            'country_name': item['country_name'],
            'country_code': item['country_code'], 
            'vm_level_id': item['vm_level_id'],
            'is_auto_renew': item['is_static'],
        }
        if not self.uid:
            res_data['pi_id'] = item['pi_id']
            res_data['email'] = item['email']
            res_data['level'] = item['level_name']
            res_data['days'] = item['days']
            res_data['cpu'] = item['cpu']
            res_data['ram'] = item['ram']
            res_data['storage'] = item['storage']
        return res_data

    def renew(self, token_id:int, payway_id:int) -> dict:
        payway = get_payway_by_id(payway_id)
        if not payway and not payway['payway'].startswith("create_vm") and payway.get('vm_level_id', 0) <= 0:
            return self.returnError(constants.ERR_NOT_PAYWAY, lang=self.lang)
        vm_obj = get_vm_by_token_id(token_id)
        if not vm_obj:  # 虚拟机不存在
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if self.uid and self.uid != vm_obj['uid']:  # 虚拟机不属于此用户
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['status']:  # 此机器创建的时候就失败了
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['online']:  # 此机器已经被删除
            return self.returnError(constants.ERR_VM_FREED, lang=self.lang)
        old_payway = get_payway_by_id(vm_obj['payway_id'])
        if not old_payway or old_payway['vm_level_id'] != payway['vm_level_id']:
            return self.returnError(constants.ERR_NOT_PAYWAY, lang=self.lang)
        order_id = set_ip_order(
            uid=self.uid, type=payway['type'], currency_type="", currency=0, receive_currency=0, is_inner=False,
            payway=payway['payway'], pay_order="", value=payway['pay_value'], extra_value=payway['extra_value'],
            status=1, checksum=True, valid=True)
        if not order_id:
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH, lang=self.lang)
        if change_user_token_life_time(token_id, payway['days']):
            return self.returnSucceed()
        else:
            refund_payway = get_vm_payway("refund_vm", payway['vm_level_id'], payway['days'])
            if refund_payway:
                set_ip_order(
                    uid=self.uid, type=refund_payway['type'], currency_type="", currency=0, receive_currency=0,
                    payway=refund_payway['payway'], pay_order="", value=refund_payway['pay_value'], is_inner=False,
                    extra_value=refund_payway['extra_value'], status=1, checksum=True, valid=True)
            return self.returnError(constants.ERR_INTERNAL_ERROR, lang=self.lang)

    def set_auto_renew(self, token_id:int, on_off:bool, payway_id:int=None) -> dict:
        vm_obj = get_vm_by_token_id(token_id)
        payway = get_payway_by_id(payway_id)
        if on_off and not payway and not payway['payway'].startswith("create_vm") and payway.get('vm_level_id', 0) <= 0:
            return self.returnError(constants.ERR_NOT_PAYWAY, lang=self.lang)
        if not vm_obj:  # 虚拟机不存在
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if self.uid and self.uid != vm_obj['uid']:  # 虚拟机不属于此用户
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['status']:    # 此机器创建的时候就失败了
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['online']:    # 此机器已经被删除
            return self.returnError(constants.ERR_VM_FREED)
        if vm_obj['is_static'] and on_off or not vm_obj['is_static'] and not on_off:
            return self.returnError(constants.ERR_VM_STATUS_NOT_CHANGE, lang=self.lang)
        if not set_vm_auto_renew(token_id, on_off=on_off, payway_id=payway_id if payway_id else vm_obj['payway_id']):
            return self.returnError(constants.ERR_INTERNAL_ERROR, lang=self.lang)
        return self.returnSucceed()

    def create(self, username, password, payway_id, country_id, auto_renew) -> dict:
        setting = get_sell_setting_by_type("create_win_wm")
        creating_vm_pi_id = db.redis.get(f"creating_win_vm_uid_{self.uid}")
        if creating_vm_pi_id:       # 用户正在创建机器
            return self.returnError(constants.ERR_VM_CREATING_WAIT, lang=self.lang)
        if setting and  not setting['on_off']:   # 开启虚拟机功能
            return self.returnError(constants.ERR_CREATED_VM_SUSPENDED)
        if self.uid and count_user_having_vm(self.uid) > setting['how_many']:   # 该用户创建机器达到上限
            return self.returnError(constants.ERR_CHARGED_LIMIT, lang=self.lang)
        try:
            location_data = get_vm_location_by_country_id(country_id)
        except:
            return self.returnError(constants.ERR_NOT_PAYWAY, lang=self.lang)
        pi_id = add_proxy_ips(status=False, port=3389, country_id=location_data['country_id'], state_id=-1, city_id=-1)
        if not pi_id:
            return self.returnError(constants.ERR_VM_CREATE_FAILED, lang=self.lang)
        payway = get_payway_by_id(payway_id)
        if not payway and not payway['payway'].startswith("create_vm") and payway.get('vm_level_id', 0) <= 0:
            return self.returnError(constants.ERR_NOT_PAYWAY, lang=self.lang)
        vm_level = get_vm_level_by_id(payway.get('vm_level_id', 0))
        if not vm_level:
            return self.returnError(constants.ERR_NOT_PAYWAY, lang=self.lang)
        size, days, ext_disk = vm_level['size'], payway['days'], vm_level['ext_disk_gb']
        order_id = set_ip_order(
            uid=self.uid, type=payway['type'], currency_type="", currency=0, receive_currency=0, is_inner=False,
            payway=payway['payway'], pay_order="", value=payway['pay_value'], extra_value=payway['extra_value'],
            status=1, checksum=True, valid=True)
        if not order_id:
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH, lang=self.lang)
        token_id = add_user_token(uid=self.uid, url="", username=username, passwd=password, pi_id=pi_id, network='rdp',
                                  life_time=days, payway_id=payway['id'], is_static=auto_renew)
        if not token_id:
            return self.returnError(constants.ERR_INTERNAL_ERROR, lang=self.lang)
        self.az.setAdmin(username, password)
        self.az.setVMCombo(location_data['location'], size)
        self.az.setExtDisk(ext_disk)
        _thread.start_new_thread(self._create_vm, (pi_id, order_id, vm_level['id'], days, setting['how_long']))
        return self.returnSucceed()

    def _create_vm(self, pi_id, order_id, vm_level_id, location, days, timeout=1800):
        db.redis.setex(f"creating_win_vm_uid_{self.uid}", timeout, pi_id)
        vm_name = constants.WIN_VM['vm_name_prefix'] + str(pi_id)
        log.info(f"正在创建机器: {vm_name}")
        try:
            self.az.createVM(vm_name)
        except:
            traceback.print_exc()
        log.info(f"已创建机器:{vm_name}")
        vm = None
        for _vm in list(self.az.getVMList().values())[::-1]:
            if vm_name == _vm['name']:
                vm = _vm
        print(vm)
        if not vm:
            log.error("创建失败, 现有机器中没有找到刚创建的机器")
            db.redis.delete("creating_win_server_lock")
            change_proxy_ips_status_online_health(pi_id, status=0, online=0, health=0)      # 设置状态为创建失败
            change_ip_order_status_by_order_id(order_id, status=2)             # 扣费账单设置为隐藏
            payway = get_vm_payway("refund_mv", vm_level_id, days)
            if payway:
                set_ip_order(
                    uid=self.uid, type=payway['type'], currency_type="", currency=0, receive_currency=0, is_inner=False,
                    payway=payway['payway'], pay_order="", value=payway['pay_value'], extra_value=payway['extra_value'],
                    status=1, checksum=True, valid=True)
            return
        log.info(f"创建成功，{vm}")
        db.redis.delete(f"creating_win_vm_uid_{self.uid}")
        change_proxy_ips_status_online_health(pi_id, status=1, online=1, health=1)
        #  保存 公网 IP ，{'name': 'proxy302-win-vm-5189', 'private_ip': '********', 'public_ip': '**************'}
        ps_id = get_supplier_by_name("win_vm")['id']
        pm_id = save_machine(ip_addr=vm['private_ip'], ps_id=ps_id, port=3389,
                             vm_name=vm['name'], public_ip=vm['public_ip'])
        insert_ip_id(pi_id, vm['public_ip'])
        change_proxy_ips_machine_and_port(pi_id, pm_id, 3389, 0)

    def delete(self, token_id:int) -> dict:
        vm_obj = get_vm_by_token_id(token_id)
        if not vm_obj:  # 虚拟机不存在
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if self.uid and self.uid != vm_obj['uid']:  # 虚拟机不属于此用户
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['status']:    # 此机器创建的时候就失败了
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['online']:    # 此机器已经被删除
            return self.returnError(constants.ERR_VM_FREED, lang=self.lang)
        _thread.start_new_thread(self._delete_vm, (vm_obj['vm_name'], vm_obj['pi_id']))
        return self.returnSucceed()

    def _delete_vm(self, vm_name, pi_id, timeout=1800):
        db.redis.set(f"deleting_mv_pi_id_{pi_id}", pi_id, timeout)
        log.info(f"正在删除机器:{vm_name}")
        try:
            res = self.az.deleteVM(vm_name)
        except:
            traceback.print_exc()
            res = None
        if res and res['del_vm'] == 0:  # 删除成功
            log.info(f"已删除机器:{vm_name}")
            change_proxy_ips_online(pi_id, online=0)
            # todo: 退剩余款
        db.redis.delete("deleting_mv_pi_id")

    def shutdown(self, token_id:int) -> dict:
        vm_obj = get_vm_by_token_id(token_id)
        if not vm_obj:  # 虚拟机不存在
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if self.uid and self.uid != vm_obj['uid']:  # 虚拟机不属于此用户
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['status']:    # 此机器创建的时候就失败了
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['online']:    # 此机器已经被删除
            return self.returnError(constants.ERR_VM_FREED, lang=self.lang)
        # if not vm_obj['health']:
        #     return self.returnError(constants.ERR_VM_SHUTDOWN, lang=self.lang)
        print(vm_obj)
        _thread.start_new_thread(self._shutdown_mv, (vm_obj['vm_name'], vm_obj['pi_id']))
        return self.returnSucceed()

    def _shutdown_mv(self, vm_name, pi_id, timeout=1800):
        db.redis.set(f"stopping_mv_pi_id_{pi_id}", pi_id, timeout)
        log.info(f"正在关机:{vm_name}")
        try:
            res = self.az.stopVM(vm_name)
        except:
            traceback.print_exc()
            res = None
        if res is not None:  #
            log.info(f"已关机:{vm_name}")
            change_proxy_ips_health(pi_id, health=0)
        db.redis.delete(f"stopping_mv_pi_id_{pi_id}")

    def startup(self, token_id:int) -> dict:
        vm_obj = get_vm_by_token_id(token_id)
        if not vm_obj:  # 虚拟机不存在
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if self.uid and self.uid != vm_obj['uid']:  # 虚拟机不属于此用户
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['status']:    # 此机器创建的时候就失败了
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['online']:    # 此机器已经被删除
            return self.returnError(constants.ERR_VM_FREED, lang=self.lang)
        # if vm_obj['health']:
        #     return self.returnError(constants.ERR_VM_STARTING, lang=self.lang)
        _thread.start_new_thread(self._startup_mv, (vm_obj['vm_name'], vm_obj['pi_id']))
        return self.returnSucceed()

    def _startup_mv(self, vm_name, pi_id, timeout=1800):
        db.redis.set(f"starting_mv_pi_id_{pi_id}", pi_id, timeout)
        log.info(f"正在开机:{vm_name}")
        try:
            res = self.az.startVM(vm_name)
        except:
            traceback.print_exc()
            res = None
        if res is not None:  #
            log.info(f"已开机:{vm_name}")
            change_proxy_ips_health(pi_id, health=1)
        db.redis.delete(f"starting_mv_pi_id_{pi_id}")

    def restart(self, token_id:int) -> dict:
        vm_obj = get_vm_by_token_id(token_id)
        if not vm_obj:  # 虚拟机不存在
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if self.uid and self.uid != vm_obj['uid']:  # 虚拟机不属于此用户
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['status']:    # 此机器创建的时候就失败了
            return self.returnError(constants.ERR_VM_NOT_EXIST, lang=self.lang)
        if not vm_obj['online']:    # 此机器已经被删除
            return self.returnError(constants.ERR_VM_FREED, lang=self.lang)
        # if vm_obj['health']:
        #     return self.returnError(constants.ERR_VM_STARTING, lang=self.lang)
        _thread.start_new_thread(self._restart_mv, (vm_obj['vm_name'], vm_obj['pi_id']))
        return self.returnSucceed()

    def _restart_mv(self, vm_name, pi_id, timeout=1800):
        db.redis.set(f"restarting_mv_pi_id_{pi_id}", pi_id, timeout)
        log.info(f"正在重启:{vm_name}")
        try:
            res = self.az.restartVM(vm_name)
        except:
            traceback.print_exc()
            res = None
        if res is not None:  #
            log.info(f"已重启:{vm_name}")
        db.redis.delete(f"restarting_mv_pi_id_{pi_id}")


