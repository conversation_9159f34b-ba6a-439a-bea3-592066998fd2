# -*- coding: utf-8 -*-
# @Time    : 2023/9/15 17:28
# <AUTHOR> hzx1994
# @File    : buy_luna_proxy.py
# @Software: PyCharm
# @description:
# from src.base import MakeProxyBase, ByArea
import constants
import db
from models.proxy import get_luminati_account, get_machine_by_id, add_proxy_ips
from src.traffic_proxy.buy_luna_proxy import BuyLunaTrafficProxy
from utils.functions import randomString


class BuySmartTrafficProxy(BuyLunaTrafficProxy):

    def get_token(self):
        country = self.country[1]
        return f"-country-{country.lower()}-session-{randomString(8)}-sessionduration-10"
        # return f"-region-{area_msg}-sessid-{randomString(8)}-sesstime-120"

    def get_la(self):
        la = get_luminati_account(type = 4)
        return la


    def choose(self):
        # 获取la
        la = self.get_la()


        # 获取pm
        pm_id = la['pm_id']
        if pm_id:  # 指定了使用哪个luminati host
            machine_obj = get_machine_by_id(pm_id)
            if not machine_obj:
                self.set_err(constants.ERR_MACHINE_DEFICIENCY)
                return 0
        # 构建curl链接
        token = self.get_token()
        host = machine_obj['domain']
        forward_port = machine_obj['forward_port_start'] or machine_obj['forward_port_end']
        if self.network_type == "socks5":
            forward_port = machine_obj['socks5_port']
        wrapped_username = "user-" + la['username'] + token
        password = la['password']
        self._la_id = la['id']
        # if self._testProxy(username=wrapped_username, password=password, host=host, port=forward_port,test_url="http://myip.lunaproxy.io/",extract_ip_fun=None) is not None:
        db_session = db.mysql
        return add_proxy_ips(ip_id=0, pm_id=machine_obj['id'], forward_port=forward_port,
                             country_id=self.country[0], state_id=self.state[0], city_id=self.city[0],
                             online=True, status=True, health=True, username=wrapped_username,
                             password=password, db_session=db_session)

        # 测试链接
        # 插入proxy
        # 构建token
        pass