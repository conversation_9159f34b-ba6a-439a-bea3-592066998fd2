# 刷新机器端口缓存
import db
from src.exceptions import *
from models.machine import *


def refresh_machine_condition(machine_id:int):
    machine_obj = get_machine_by_id(machine_id)
    if not machine_obj:
        raise NoMachineError()
    supplier_obj = get_supplier_by_id(machine_obj['ps_id'])
    print(supplier_obj)
    if not supplier_obj or supplier_obj['name'] != "911":
        raise MachineSupplierError()
    machine_free_proxy = __get_machine_free_proxy(machine_obj)
    db.redis.hset("machines_of_911", f"911_machine_{machine_id}", len(machine_free_proxy))


def refresh_machines_use_condition():
    machines = get_machines_by_supplier("911")
    machines_free_port = {machine['id']: __get_machine_free_proxy(machine) for machine in machines}
    db.redis.delete("machines_of_911")
    db.redis.hmset("machines_of_911", {f"911_machine_{key}": len(machines_free_port[key])
                                       for key in machines_free_port.keys()})


def __get_machine_free_proxy(machine):
    machine_port_range = range(machine['forward_port_start'], machine['forward_port_end'] + 1)
    machine_using_port = get_machine_using_forward_port(machine['id'])
    machine_free_port = list(set(machine_port_range) - (set(machine_using_port)))
    machine_free_port.sort()
    db.redis.delete(f"911_machine_{machine['id']}")
    if machine_free_port:
        db.redis.rpush(f"911_machine_{machine['id']}", *machine_free_port)
        return machine_free_port
    else:
        return []
