import subprocess
import random
import json


class AZController:
    _admin_username = None
    _admin_password = None
    _image = None
    _location = None
    _size = None
    _ext_disk_gb = None
    _nsg = None

    def __init__(self, group, vm_name_prefix, admin_username=None, admin_password=None,
                 image=None, location=None, size=None):
        """
        设置对应的资源组和虚拟机前缀
        :param group:           资源组名称 : eg: "Proxy302"
        :param vm_name_prefix:  虚拟机前缀名称: eg "proxy302-winserver-"
        :param admin_username:  虚拟机管理员用户名
        :param admin_password:  虚拟机管理员密码
        :param image:           虚拟机的镜像源
        :param location:        区域
        :param size:            大小
        """
        self._group, self._vm_name_prefix = group, vm_name_prefix
        self.setAdmin(admin_username, admin_password)
        self.setVMCombo(location, size)
        self.image = image

    @property
    def image(self):
        return self._image

    @image.setter
    def image(self, _image):
        """
        设置映像源
        :param _image:  映像源在Azure控制台中获取 如： "/subscriptions/e85ed837-2862-4136-a3ba-0a95684fec6d/resourceGroups/Proxy302/providers/Microsoft.Compute/galleries/Proxy302/images/proxy302-winserver-base-image1"
        """
        self._image = _image

    def setAdmin(self, username, password):
        """
        设置机器的管理员账号
        :param username:    账号名
        :param password:    账号密码
        """
        self._admin_username, self._admin_password = username, password

    def setVMCombo(self, location, size):
        """
        设置套餐
        :param location:    地区: eg:  "eastasia"
        :param size:        套餐大小标准： eg: "Standard_B1ms"
        :return:
        """
        self._location, self._size = location, size

    def setExtDisk(self, ext_disk_gb:int=None):
        """
        额外添加虚拟磁盘
        :param ext_disk_gb:     额外添加的虚拟磁盘的大小 单位GB
        :return:
        """
        if type(ext_disk_gb) == int and ext_disk_gb >= 0:
            self._ext_disk_gb = ext_disk_gb
        else:
            raise TypeError('ext_disk_gb must int')

    def setNSG(self, nsg_name:str=None):
        """
        添加安全组，默认会新建一个安全组
        :param nsg_name: 安全组名
        :return:
        """
        if nsg_name and isinstance(nsg_name, str):
            self._nsg = nsg_name
        else:
            raise TypeError('nsg name must str')

    def getVMList(self):
        """
        获取虚拟机列表
        """
        cmd = f"""az vm list-ip-addresses -g {self._group}"""
        print(f"run: {cmd}")
        res = json.loads(subprocess.check_output(cmd, shell=True))
        # print(json.dumps(res_json))
        vm_data = {int(vm['virtualMachine']['name'].replace(self._vm_name_prefix, "")): {
            'name': vm['virtualMachine']['name'],
            'private_ip': vm['virtualMachine']['network']['privateIpAddresses'][0],
            'public_ip': vm['virtualMachine']['network']['publicIpAddresses'][0]['ipAddress'],
        } for vm in res if vm['virtualMachine']['name'].lower().startswith(self._vm_name_prefix)}
        return vm_data

    def getVMData(self, vm_name:str):
        """
        获取单个虚拟机的状态信息
        :param vm_name:     虚拟机名
        :return:
        """
        cmd = f"az cm get-instance-view -g {self._group} -n {vm_name}"
        print(f"run: {cmd}")
        res = subprocess.check_output(cmd, shell=True)
        try:
            res = json.loads(res)
            return res
        except:
            return None

    def __checkCreateVMParameters(self):
        """
        检查创建虚拟机的时候参数是否完整
        """
        if self.image is None:
            raise ValueError("Parameter: image is not specified. Please use .image='xxxx' to set it.")
        if self._admin_username is None:
            raise ValueError("Parameter: username is not specified. Please use function .setAdmin(username, password) to set it")
        if self._admin_password is None:
            raise ValueError("Parameter: password is not specified. Please use function .setAdmin(username, password) to set it")
        if self._location is None:
            raise ValueError("Parameter: location is not specified. Please use function .setVMCombo(location, size) to set it")
        if self._size is None:
            raise ValueError("Parameter: size is not specified. Please use function .setVMCombo(location, size) to set it")

    def createVM(self, vm_name=None):
        """
        创建虚拟机
        :param vm_name: 虚拟机名称， 为None时候使用  前缀加后面数字的叠加
        """
        self.__checkCreateVMParameters()
        if not vm_name:
            vm_list = self.getVMList()
            vm_name = self._vm_name_prefix + str(max(vm_list.keys()) + 1).rjust(2, "0")
        cmd = f"""az vm create -g {self._group} -n {vm_name} --admin-username {self._admin_username} --admin-password {self._admin_password} --image "{self._image}"  --location {self._location} --size {self._size} --computer-name {self._admin_username}"""
        if self._ext_disk_gb:
            cmd += f" --data-disk-sizes-gb {self._ext_disk_gb}"
        if self._nsg:
            cmd += f" --nsg {self._nsg}"
        print(f"run: {cmd}")
        res = subprocess.call(cmd, shell=True)
        return vm_name

    def startVM(self, vm_name=None, vm_suffix_number=None):
        """
        开机
        :param vm_name:             要设置的安全组的名称
        :param vm_suffix_number:    前缀后的序号（和vm_name只能选其中一个）
        """
        if (vm_name is None) + (vm_suffix_number is None) != 1:
            raise ValueError("参数： vm_name 和 vm_suffix_name 必须并且只能其中一个需要传递参数")
        if not vm_name:
            vm_name = self._vm_name_prefix + str(vm_suffix_number).rjust(2, "0")
        cmd = f"""az vm start -g {self._group} -n {vm_name}"""
        print(f"run: {cmd}")
        res = subprocess.call(cmd, shell=True)
        return res

    def stopVM(self, vm_name=None, vm_suffix_number=None):
        """
        关机
        :param vm_name:             要设置的安全组的名称
        :param vm_suffix_number:    前缀后的序号（和vm_name只能选其中一个）
        """
        if (vm_name is None) + (vm_suffix_number is None) != 1:
            raise ValueError("参数： vm_name 和 vm_suffix_name 必须并且只能其中一个需要传递参数")
        if not vm_name:
            vm_name = self._vm_name_prefix + str(vm_suffix_number).rjust(2, "0")
        cmd = f"""az vm stop -g {self._group} -n {vm_name}"""
        print(f"run: {cmd}")
        res = subprocess.call(cmd, shell=True)
        return res

    def restartVM(self, vm_name=None, vm_suffix_number=None):
        """
        重启机器
        :param vm_name:             要设置的安全组的名称
        :param vm_suffix_number:    前缀后的序号（和vm_name只能选其中一个）
        """
        if (vm_name is None) + (vm_suffix_number is None) != 1:
            raise ValueError("参数： vm_name 和 vm_suffix_name 必须并且只能其中一个需要传递参数")
        if not vm_name:
            vm_name = self._vm_name_prefix + str(vm_suffix_number).rjust(2, "0")
        cmd = f"""az vm restart -g {self._group} -n {vm_name}"""
        print(f"run: {cmd}")
        res = subprocess.call(cmd, shell=True)
        return res

    def openVNetPort(self, vm_name=None, vm_suffix_number=None):
        """
        设置开放安全组局域网端口
        :param vm_name:             要设置的安全组的名称
        :param vm_suffix_number:    前缀后的序号（和vm_name只能选其中一个）
        """
        if (vm_name is None) + (vm_suffix_number is None) != 1:
            raise ValueError("参数： vm_name 和 vm_suffix_name 必须并且只能其中一个需要传递参数")
        if not vm_name:
            vm_name = self._vm_name_prefix + str(vm_suffix_number).rjust(2, "0")
        priority = random.randint(1001, 2000)
        cmd = f"""az network nsg rule create -g {self._group} --nsg-name {vm_name}NSG -n Port_8080 --priority {priority} --source-asgs {self._group} --destination-port-ranges "*" --destination-asgs {self._group} --access Allow --protocol "*" """
        print(f"run: {cmd}")
        res = subprocess.call(cmd, shell=True)
        return res

    def deleteVM(self, vm_name=None, vm_suffix_number=None):
        """
        删除虚拟机及其对应的产物
        :param vm_name:             要设置的安全组的名称
        :param vm_suffix_number:    前缀后的序号（和vm_name只能选其中一个）
        :return 成功返回 {'del_vm': 0, 'del_disk': 0, 'del_nic': 0, 'del_nsg': 0, 'del_ip': 0}
        """
        if (vm_name is None) + (vm_suffix_number is None) != 1:
            raise ValueError("参数： vm_name 和 vm_suffix_name 必须并且只能其中一个需要传递参数")
        if not vm_name:
            vm_name = self._vm_name_prefix + str(vm_suffix_number).rjust(2, "0")
        res_dict = dict(del_vm=None, del_disk=None, del_nic=None, del_nsg=None, del_ip=None)
        res_dict['del_vm'] = self._delVMachine(vm_name)
        res_dict['del_disk'] = self._delDisk(vm_name)
        res_dict['del_nic'] = self._delNic(vm_name)
        res_dict['del_nsg'] = self._delNsg(vm_name)
        res_dict['del_ip'] = self._delIP(vm_name)
        return res_dict

    def _delVMachine(self, vm_name):
        """
        删除虚拟机
        :param vm_name: 资源对应的虚拟机名称
        :return 成功返回0
        """
        cmd = f"""az vm delete -g {self._group} -n {vm_name} --yes """
        print(f"run: {cmd}")
        return subprocess.call(cmd, shell=True)

    def __listDisk(self):
        """
        获取虚拟硬盘列表
        :param vm_name: 资源对应的虚拟机名称
        :return 成功返回0
        """
        cmd = f"""az disk list -g {self._group} """
        res = json.loads(subprocess.check_output(cmd, shell=True))
        disk_list = [disk['name'] for disk in res if disk['name'].startswith(self._vm_name_prefix)]
        return disk_list

    def _delDisk(self, vm_name):
        """
        删除虚拟硬盘
        :param vm_name: 资源对应的虚拟机名称
        :return 成功返回0
        """
        disk_names = self.__listDisk()
        result = 0
        for disk in disk_names:
            if disk.startswith(vm_name):
                cmd = f"""az disk delete -g {self._group} -n {disk} --yes """
                print(f"run: {cmd}")
                res = subprocess.call(cmd, shell=True)
                if res != 0:
                    result = res
        return result

    def _delNic(self, vm_name):
        """
        删除虚拟网卡
        :param vm_name: 资源对应的虚拟机名称
        :return 成功返回0
        """
        cmd = f"""az network nic delete -g {self._group} -n {vm_name}VMNic """
        print(f"run: {cmd}")
        return subprocess.call(cmd, shell=True)

    def _delNsg(self, vm_name):
        """
        删除安全组
        :param vm_name: 资源对应的虚拟机名称
        :return 成功返回0
        """
        cmd = f"""az network nsg delete -g {self._group} -n {vm_name}NSG """
        print(f"run: {cmd}")
        return subprocess.call(cmd, shell=True)

    def _delIP(self, vm_name):
        """
        删除公网IP
        :param vm_name: 资源对应的虚拟机名称
        :return 成功返回0
        """
        cmd = f"""az network public-ip delete -g {self._group} -n {vm_name}PublicIP """
        print(f"run: {cmd}")
        return subprocess.call(cmd, shell=True)

