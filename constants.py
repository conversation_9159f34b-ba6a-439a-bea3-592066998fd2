#/usr/bin/env python
# -*- coding:utf-8 -*-

# Copyright 2020

# @author: <PERSON>Moo

import os.path
from typing import Union
from libs.feishu import Fei<PERSON>hu


EXPORT_HOST = ""         # 服务器的域名
EXPORT_PORT = {'http': 2222, 'socket': 3333, 'socks': 3333, 'socks5': 333}    # 代理端口（http:2222， socket：3333）
REQUEST_MACHINE_URL = "http://{machine_ip}:8080/v1/proxy"

mysql_config = {}

PROXY_SERVERS_LIST = []
CN_PROXY_SERVERS_LIST = []

WIN_VM = None       # type: Union[dict, None]
AZURE_IP = None       # type: Union[dict, None]

feishu = None       # type: Union[FeiShu, None]

DESTROY_IP_URL = ""
DESTROY_IP_USERNAME = ""
DESTROY_IP_PASSWORD = ""

IPROYAL_TOKEN = ""

""" 全局配置常量 """
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
STATIC_DIR = os.path.join(BASE_DIR, 'static')
TEMPLATE_DIR = os.path.join(BASE_DIR, "template")
DOWNLOAD_DIR = os.path.join(BASE_DIR, "download")

SETTINGS_FILE = "settings.yaml"


""" IP INFO """
IP_INFO_URL = "https://ipinfo.io/{_ip}/json"
IP_INFO_TOKEN = ""

""" IPIP """
IPIP_URL = "http://myip.ipip.net/"

""" 协议相关 """
ERR_PROTOCOL_ERROR = (-1001, "协议解析错误", "Protocol parsing error")
ERR_INTERNAL_ERROR = (500, "内部错误", "Internal error")
ERR_TIMEOUT_ERROR = (504, "操作超时", "Operation timeout")

""" 接口相关 """
ERR_ARGUMENT_MISSING = (1001, "缺少参数", "Missing parameter")
ERR_ARGUMENT_TYPE = (1002, "参数类型错误", "Parameter type error")

""" 用户相关 """
ERR_NO_USER = (2001, "邮箱或者密码错误", "email or password error")   # "用户不存在", "User does not exist")
ERR_HAVE_USER = (2002, "用户已存在", "User already exists")
ERR_PWD_ERROR = (2003, "邮箱或者密码错误", "email or password error") # "密码错误", "Wrong password")
ERR_REGISTER_ERROR = (2005, "注册失败", "Registration failed")
ERR_TOKEN_ERROR = (2004, "身份认证失败", "Identity authentication failed")
ERR_LOGOUT_ERROR = (2006, "退出登录失败", "Logout failed")
ERR_BAN_LOGIN = (2007, "登陆错误超过10次，请10分钟后再尝试", "There are more than 10 login errors, please try again after 10 minutes")
ERR_BLACKLIST_HAVING = (2007, "黑名单已存在此邮箱", "This email exists in the blacklist")
ERR_BLACKLIST_NOT_HAVING = (2007, "黑名单不存在此邮箱", "This email not exists in the blacklist")
ERR_EMAIL_BLACKED = (2008, "你的邮箱账号行为有异常，请联系我们寻求帮助。", "Your email has abnormal behavior, please message us for help.")


""" 账单相关 """
ERR_CHARGE_ERROR = (3001, "充值失败", "Recharge failed")
ERR_BALANCE_NOT_ENOUGH = (3002, "余额不足", "Insufficient balance")
ERR_PRICE_ERROR = (3003, "金额错误", "Wrong amount")
ERR_NOT_PAYWAY = (3004, "套餐选择有误", "Wrong package selection")
ERR_CHARGED_LIMIT = (3005, "你已达到该套餐的购买次数上限", "You have reached the maximum number of purchases for this package")

""" 代理相关 """
ERR_AREA_ERROR = (4001, "选择地区有误", "Wrong selecting location")
ERR_MACHINE_DEFICIENCY = (4002, "当前没有可用的机器，请稍后重试。", "There are currently no available machines, please try again later.")
ERR_NEW_PROXY_FREQUENTLY = (4003, "生成代理过于频繁，请1秒后重试。", "The proxy is generated too frequently. Please try again in 1 second.")
ERR_NEW_PROXY_FAILED = (4004, "没有对应地区/IP的代理", "There is no proxy corresponding to the region/IP.")
ERR_NO_THIS_USER_TOKEN = (4005, "没有此代理记录，请核实代理账号用户和密码。", "No such record, please verify the proxy account user and password.")
ERR_NO_THIS_PROXY = (4006, "代理不存在", "No this proxy")
ERR_POSSESSING_PROXY = (4006, "已经拥有此代理", "possessing this proxy")
ERR_PROXY_USED_TOO_MORE = (4007, "代理已被他人被购买，请选择其他代理。", "The proxy has been purchased, please choose another proxy.")
ERR_PROXY_ONLY_HTTP = (4008, "此代理只支持HTTP网络协议类型。", "This proxy only support the HTTP network protocol type.")
ERR_PROXY_UNAVAILABLE = (4009, "此代理目前不可用，请选择其他代理。", "This proxy is not available now, please choose another proxy.")
ERR_SEARCH_STATIC_PROXY_BY_IP_LIMIT = (4010, "通过IP地址搜索静态IP代理次数达到上限，购买一个静态IP代理之后恢复。", "The number of searches for a static IP proxy by IP address reaches the upper limit, and it will be restored after buying a residential static proxy")
ERR_SEARCH_STATIC_PROXY_BY_AREA_LIMIT = (4010, "静态IP代理搜索次数达到上限，购买一个静态IP代理之后恢复。", "The number of searches for a static IP proxy by location reaches the upper limit, and it will be restored after buying a residential static proxy")
ERR_PROXY_TOKEN_EXPIRED = (4011, "代理已经过期", "The proxy has expired.")
ERR_PROXY_NOT_IN_NORMAL = (4012, "代理现在不是处于正常状态", "The proxy is not in normal state now.")
ERR_PROXY_NOT_IN_EXPIRED = (4012, "代理现在不是处于过期状态", "The proxy is not in expired state now.")
ERR_PROXY_LOST_EFFICACY = (4013, "此代理暂时停用", "The proxy loss efficacy")
ERR_PROXY_AREA_ERROR = (4014, "选中地区较为特殊，请联系客服处理", "The selected area is special, please contact customer service.")
""" Azure静态代理相关 """
ERR_AZURE_LIMIT = (4101, "生成静态私有代理达到并发上限，请等待生成成功后再尝试。", "The generation of static private proxy has reached the concurrency limit. Please wait for the generation to succeed before trying again.")

""" open api 相关 """
ERR_API_TOKEN_ERROR = (5001, "API token有误，请核实后再提交。", "Wrong api-key")
ERR_API_TOKEN_TO_MORE = (5002, "创建失败，API token达到最大允许创建数量。", "Creation failed, API token reached the maximum allowable number of creation.")
ERR_NO_API_TOKEN= (5003, "不存在此API token", "Api-key does not exist")

""" win虚拟机相关 """
ERR_VM_CREATE_FAILED = (20001, "远程云虚拟机创建失败", "Failed to create remote cloud virtual machine")
ERR_CREATED_VM_SUSPENDED = (20002, "远程创建云虚拟机功能暂停使用", "The remote virtual machine creation function is suspended")
ERR_VM_CREATE_LIMIT = (20003, "创建远程云虚拟机数量达到个人上限", "The number of remote cloud virtual machines created reaches the personal limit")
ERR_VM_CREATING_WAIT = (20004, "另外一台远程云虚拟机正在创建中，请稍后。", "Another remote cloud virtual machine is being created, please wait.")
ERR_VM_NOT_EXIST = (20005, "虚拟机不存在", "This virtual machine does not exist")
ERR_VM_STARTING = (20006, "虚拟机正处于开机状态", "The virtual machine is powering on")
ERR_VM_SHUTDOWN = (20006, "虚拟机正处于关机状态", "The virtual machine is powering off")
ERR_VM_FREED = (20007, "虚拟机已被释放", "The virtual machine is freed")
ERR_VM_STATUS_NOT_CHANGE = (20008,"状态无效改变", "You don't have to change the state")


