import os
import requests

from log import get_logger

log = get_logger(__name__)


class FeiShu:
    url = "https://open.feishu.cn/open-apis/bot/v2/hook/"
    headers = {'Content-Type': "application/json"}
    def __init__(self, id, secret=""):
        self.id, self.secret = id, secret

    def request(self, post_data:dict) -> bool:
        url = self.url + self.id
        log.info(f"request:{url}  data:{post_data}")
        try:
            res = requests.post(url=url, json=post_data, headers=self.headers, timeout=5)
            log.info(res.text)
            if res.status_code == 200:
                log.info("send succeed")
                return True
            else:
                log.info(f"send failed: status={res.status_code}")
                return False
        except Exception as e:
            log.info(f"send failed: {e}")
            return False

    def sendText(self, text:str) -> bool:
        post_data = {
            'msg_type': "text",
            'content': {
                'text': text
            }
        }
        return self.request(post_data)

    def sendPost(self, title: str, content: list) -> bool:
        post_data = {
            'msg_type': "post",
            'content': {
                'post': {
                    'zh_cn': {
                        'title': title,
                        'content': content
                    }
                }
            }
        }
        return self.request(post_data)
