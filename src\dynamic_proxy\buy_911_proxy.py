import abc
import ipad<PERSON>
from typing import List
import requests
import constants
import db
import utils
import random
import pandas as pd
from models.proxy import get_first_using_proxy, change_proxy_ips_online
from src.dynamic_proxy.refresh_machines_ports import refresh_machines_use_condition, refresh_machine_condition
from src.exceptions import MachinePortInsufficientError, NoMachineError, NoProxyError
from models.machine import *
from src.base import MakeProxyBase, ByIP, ByArea
import time

from utils.decorators import timer
from utils.functions import ip_verify, get_ip_area


class BeforeRequestMachine:
    """ 请求机器开代理时的装饰器 """
    def __init__(self, foo):
        self.foo = foo

    def __call__(self, *args, **kwargs):
        self.machine_id, self.machine_ip = kwargs.get("machine_id", None), kwargs.get("machine_ip", None)
        if self.machine_id is None:
            raise TypeError("missing required positional argument: 'machine_id' ")
        self.lockMachine()
        res =  self.foo(*args, **kwargs)
        self.unlockMachine()
        self.machineAlarm(res)
        return res

    def lockMachine(self):
        """ 等待机器解锁，并上锁 """
        while db.redis.get(f"lock_machine_{self.machine_id}"):  # 等待解锁
            pass
        db.redis.setex(f"lock_machine_{self.machine_id}", 60 * 2, time.time())  # 上锁

    def unlockMachine(self):
        """ 解锁机器 """
        db.redis.delete(f"lock_machine_{self.machine_id}")  # 解锁

    def machineAlarm(self, res):
        """ 根据返回结果判定是否故障报警 """
        err_history = db.redis.get(f"req_err_{self.machine_id}")
        if not err_history and res < 0:
            fault_machine_num = db.redis.get(f"fault_machine_id_{self.machine_id}")
            fault_machine_num = int(fault_machine_num) if fault_machine_num else 0
            log.warning(f"{self.machine_ip}机器 请求生成代理失败 {fault_machine_num + 1}次")
            if (
                fault_machine_num
                and fault_machine_num > 2
                and constants.feishu.sendText(f"{self.machine_ip}机器 请求生成代理失败")
            ):
                db.redis.setex(f"req_err_{self.machine_id}", 60 * 60 * 24, 1)
        elif err_history and res >= 0:
            if constants.feishu.sendText(f"{self.machine_ip}机器 请求生成代理恢复正常"):
                db.redis.delete(f"req_err_{self.machine_id}")


@BeforeRequestMachine
@timer
def requestMachineNewProxy(machine_id, machine_ip, data) -> int:
    """
    请求机器申请代理
    :param machine_ip:  机器的IP
    :param data:        post请求参数
    :return:    返回生成代理的big_int类型的ip 申请失败返回0, 因为机器故障申请失败的返回-1
    """
    machine_ip = ip_verify(machine_ip)
    url = constants.REQUEST_MACHINE_URL.format(machine_ip=str(machine_ip))
    log.info(f"requesting: POST  {url}    data:{data}    machineID:{machine_id}")
    # # # 测试代码
    # import random
    # result_ip = f"69.243.{random.randint(0, 255)}.{random.randint(0, 255)}"
    # ip_id = int(ipaddress.ip_address(result_ip))
    # return ip_id
    try:
        res = requests.post(url=url, json=data, timeout=20)
        if res.status_code != 200:  # http_code != 200  返回-1
            return -1
        # 返回成功
        log.info(f"result: {res.text}")
        if not res.text:
            return 0
        res_data = res.json()
        if res_data['code'] != 0 or res_data['data'] is None :  # 有返回，但是申请的时候可能没有符合的代理或者端口错误 等
            return 0
        # 申请成功
        result_ip = res_data['data']['ip']
        ip_code = int(ipaddress.ip_address(result_ip))  # IP从字符转int
        return ip_code
    except:  # 这里请求失败或者超时  返回-1
        log.error(traceback.format_exc())
        return -1

@timer
def request2Destroy(machine_ip, pi_id) -> bool:
    machine_ip = ip_verify(machine_ip)
    machine_ip_addr = str(machine_ip)
    url = constants.REQUEST_MACHINE_URL.format(machine_ip=machine_ip_addr)
    params = {'taskId': pi_id}
    log.info(f"request:  {url}   params: {params}")
    try:
        res = requests.delete(url=url, params=params, timeout=5)
        if res.status_code == 200:
            res_data = res.json()
            log.info(res_data)
            res2 = requests.post(url=constants.DESTROY_IP_URL, data={'pi_id': pi_id},  timeout=2,
                                 auth=(constants.DESTROY_IP_USERNAME, constants.DESTROY_IP_PASSWORD))
            if res2.status_code == 200:
                log.info(res2.json())
            return True
        else:
            return False
    except Exception as e:
        log.error(e)
        return False


class Choose911MachineMode:

    def __init__(self, ex_machines_id_list:list=None):
        """
        :param ex_machines_id_list: 制定要排除掉的 机器的id列表
        """
        self.ex_mids_list = ex_machines_id_list

    @abc.abstractmethod
    def chooseMachineKey(self, machines_condition:dict) -> str:
        """
        选择合适的代理机器（Windows server）
        :returns  选择目标机器在缓存中的key
        """
        pass

    @staticmethod
    def _recoverOnePort(machine_id, machine_port):
        """
        在rides缓存中恢复一个端口为可用
        :param machine_id:      机器的ID
        :param machine_port:    端口
        """
        db.redis.rpush(f"911_machine_{machine_id}", machine_port)
        now_num = int(db.redis.hget("machines_of_911", f"911_machine_{machine_id}"))
        db.redis.hset("machines_of_911", f"911_machine_{machine_id}", now_num + 1)

    def _useOldestProxyPort(self) -> List[int]:
        """
        使用最早
        :return:  返回机器的ID， 端口
        """
        oldest_proxy_obj = get_first_using_proxy()
        print(oldest_proxy_obj)
        if oldest_proxy_obj:
            if change_proxy_ips_online(oldest_proxy_obj['id'], 0):
                # self._recoverOnePort(oldest_proxy_obj['pm_id'], oldest_proxy_obj['forward_port'])
                return oldest_proxy_obj['pm_id'], oldest_proxy_obj['forward_port']
        else:
            return 0, 0

    def get911MachineDataInCache(self) -> dict:
        machines_condition_in_redis = db.redis.hgetall("machines_of_911")  # type:dict
        if not machines_condition_in_redis:     # redis中没有machines的数据刷新缓存里面的数据
            refresh_machines_use_condition()
            machines_condition_in_redis = db.redis.hgetall("machines_of_911")  # type:dict
        machines_list = get_machines_by_supplier("911")
        ex_mids = self.ex_mids_list or []
        normal_machines_id = [machine['id'] for machine in machines_list if
                              (not machine['fault'] and machine['id'] not in ex_mids)]  # 排除掉故障的机子和制定排除的机子
        machines_condition = {key.decode('utf-8'): int(machines_condition_in_redis[key])
                              for key in machines_condition_in_redis.keys() if
                              int(machines_condition_in_redis[key]) > 0
                              and int(key.decode('utf-8').replace("911_machine_", "")) in normal_machines_id}
        print(f"machines_condition_in_redis -> {machines_condition_in_redis}")
        print(f"ex_mids -> {ex_mids}")
        print(f"normal_machines_id -> {normal_machines_id}")
        print(f"machines_condition -> {machines_condition}")
        return machines_condition

    def choose(self): # -> (int, str, int):
        """
        选择合适的代理机器(Windows server)
        :returns  (目标机器的id, 目标机器的ip, 需要连接的端口)
        """
        machines_condition = self.get911MachineDataInCache()
        if machines_condition:   # 还有端口的情况  使用不同的模式去选择代理的机器
            target_machine_key = self.chooseMachineKey(machines_condition)
            target_machine_id = int(target_machine_key.replace("911_machine_", ""))
            target_port = int(db.redis.lpop(target_machine_key))
        else:       # 所有的机子都没有端口  选用最早生成的 一个端口
            target_machine_id, target_port = self._useOldestProxyPort()  # 取之前申请的代理中最早的一个代理 的机器和端口
            print(f"oldest target: ID:{target_machine_id}  PORT:{target_port}")
            if target_machine_id == 0:  # 可以覆盖的端口也没有了 报没有机器的错误错
                log.warning("没有可以使用的机器")
                raise MachinePortInsufficientError()
        target_machine_obj = get_machine_by_id(target_machine_id)
        if not target_machine_obj:
            raise MachinePortInsufficientError()
        target_machine_ip = str(ipaddress.ip_address(target_machine_obj['ip_addr']))
        self.supplier_id = target_machine_obj['ps_id']
        log.info(f"choose machine:  ID：{target_machine_id}  ip:{target_machine_ip}  port:{target_port}")
        return target_machine_id, target_machine_ip, target_port


class Choose911MachineRandomMode(Choose911MachineMode):
    def __init__(self, ex_machines_id_list:list=None):
        super().__init__(ex_machines_id_list)
        log.info("<< 选用随机机器方法 >>")

    def chooseMachineKey(self, machines_condition:dict) -> str:
        random_machine_key = random.choices(tuple(machines_condition.keys()), k=1)[0]
        return random_machine_key


class Choose911MachineMinPortBlockingMode(Choose911MachineMode):
    def __init__(self, ex_machines_id_list:list=None):
        super().__init__(ex_machines_id_list)
        log.info("<< 选用最少端口机器阻塞方法 >>")

    def chooseMachineKey(self, machines_condition: dict):
        min_port_machine = min(machines_condition, key=machines_condition.get)  # type:  str   # 直接取最少端口的机器
        return min_port_machine


class Choose911MachineMaxPortBlockingMode(Choose911MachineMode):
    def __init__(self, ex_machines_id_list:list=None):
        super().__init__(ex_machines_id_list)
        log.info("<< 选用最多端口机器阻塞方法 >>")

    def chooseMachineKey(self, machines_condition:dict) -> str:
        max_port_machine = max(machines_condition, key=machines_condition.get)  # type:  str   # 直接取最多端口的机器
        return max_port_machine


class Choose911MachineMinPortNonBlockingMode(Choose911MachineMode):
    def __init__(self, ex_machines_id_list: list = None):
        super().__init__(ex_machines_id_list)
        log.info("<< 选用最少端口机器非阻塞方法 >>")

    def chooseMachineKey(self, machines_condition: dict):
        requesting_machine = db.redis.keys("lock_machine_*")
        if requesting_machine:
            requesting_machine_id = [machine.decode('utf-8').replace("lock_machine_", "911_machine_")
                                     for machine in requesting_machine]
        else:
            requesting_machine_id = []
        non_blocking_machines_condition = {k: v for k, v in machines_condition.items()
                                           if k not in requesting_machine_id}
        if non_blocking_machines_condition:
            min_port_machine = min(non_blocking_machines_condition, key=non_blocking_machines_condition.get)
        else:
            min_port_machine = min(machines_condition, key=machines_condition.get)  # type:  str   # 直接取最少端口的机器
        if not min_port_machine:   # 如果所有机器都在运请求 随机一个机器
            random_machine_key = random.choices(tuple(machines_condition.keys()), k=1)[0]
            return random_machine_key
        return min_port_machine


class Choose911MachineMaxPortNonBlockingMode(Choose911MachineMode):
    def __init__(self, ex_machines_id_list:list=None):
        super().__init__(ex_machines_id_list)
        log.info("<< 选用最多端口机器非阻塞方法 >>")

    def chooseMachineKey(self, machines_condition: dict) -> str:
        requesting_machine = db.redis.keys("lock_machine_*")
        if requesting_machine:
            requesting_machine_id = [machine.decode('utf-8').replace("lock_machine_", "911_machine_")
                                     for machine in requesting_machine]
        else:
            requesting_machine_id = []
        non_blocking_machines_condition = {k: v for k, v in machines_condition.items()
                                           if k not in requesting_machine_id}
        if non_blocking_machines_condition:
            min_port_machine = max(non_blocking_machines_condition, key=non_blocking_machines_condition.get)
        else:
            min_port_machine = max(machines_condition, key=machines_condition.get)  # type:  str   # 直接取最少端口的机器
        if not min_port_machine:   # 如果所有机器都在运请求 随机一个机器
            random_machine_key = random.choices(tuple(machines_condition.keys()), k=1)[0]
            return random_machine_key
        return min_port_machine


class Choose911MachineFactory:

    modes = {
        'RANDOM_MACHINE_MODE': Choose911MachineRandomMode,  # 随机机器模式
        'MIN_PORT_MACHINE_BLOCKING_MODE': Choose911MachineMinPortBlockingMode,  # 最少端口阻塞模式
        'MAX_PORT_MACHINE_BLOCKING_MODE': Choose911MachineMaxPortBlockingMode,  # 最多端口阻塞模式
        'MIN_PORT_MACHINE_NON_BLOCKING_MODE': Choose911MachineMinPortNonBlockingMode,  # 最少端口非阻塞模式
        'MAX_PORT_MACHINE_NON_BLOCKING_MODE': Choose911MachineMaxPortNonBlockingMode,  # 最大端口非阻塞模式
    }
    # default_mode = 'RANDOM_MACHINE_MODE'
    default_mode = 'MIN_PORT_MACHINE_BLOCKING_MODE'

    def target(self, ex_machine_id_list:list=None): # (int, str, int):
        if isinstance(ex_machine_id_list, int):
            ex_machine_id_list = [ex_machine_id_list]
        if not isinstance(ex_machine_id_list, (list, tuple)):
            raise TypeError(": ids")
        return self._chooseMachine(ex_machine_id_list)

    def _chooseMachine(self, ex_machine_id_list:list=None): # -> (int, str, int):
        """
        选择合适的代理机器（Windows server）
        :returns  (目标机器的id， 目标机器的ip， 需要连接的端口)
        """
        mode = db.redis.get("choose_911_machine_mode")
        mode = self.modes.get(mode.decode('utf-8').upper() if mode else "", None)
        if not mode:
            mode = self.modes[self.default_mode]
            db.redis.set("choose_911_machine_mode", self.default_mode)
        return mode(ex_machine_id_list).choose()


class BuyProxy(MakeProxyBase):
    _use_machine_id = None
    RETRY_LIMIT = 5
    retry_num = 0
    pi_id, data = None, None

    def __init__(self):
        self.ex_mids = []

    @property
    def use_machine_id(self):
        return self._use_machine_id

    @use_machine_id.setter
    def _ss(self, machine_id:int=None):
        if machine_id is not None:
            machine_cache = db.redis.hget("machines_of_911", f"911_machine_{machine_id}")
            if not machine_cache:
                refresh_machine_condition(machine_id)
                machine_cache = db.redis.hget("machines_of_911", f"911_machine_{machine_id}")
            if not machine_cache:
                raise NoMachineError()
            if int(machine_cache) < 1:
                raise MachinePortInsufficientError()
        self._use_machine_id = machine_id

    @staticmethod
    def _recoverOnePort(machine_id, machine_port):
        """
        在rides缓存中恢复一个端口为可用
        :param machine_id:      机器的ID
        :param machine_port:    端口
        """
        db.redis.rpush(f"911_machine_{machine_id}", machine_port)
        now_num = int(db.redis.hget("machines_of_911", f"911_machine_{machine_id}"))
        db.redis.hset("machines_of_911", f"911_machine_{machine_id}", now_num + 1)

    @staticmethod
    def _expenditureOnePort(machine_id):
        """
        在rides缓存中恢复一个端口为可用
        :param machine_id:      机器的ID
        :param machine_port:    端口
        """
        now_num = int(db.redis.hget("machines_of_911", f"911_machine_{machine_id}"))
        db.redis.hset("machines_of_911", f"911_machine_{machine_id}", now_num - 1)

    def check_area(self,res_ip,port):
        ip = str(ipaddress.ip_address(res_ip))
        res = requests.get(f"https://ipinfo.io/{ip}/?token=53ecb41795ebf0", timeout=5)
        if res.status_code != 200:
            print(f'911_{self.uid}_res:{res}')
            return 0
        res_data = res.json()
        ip_id = db.mysql.get("select id from t_ips where ip_addr=%s", res_ip)
        print(f"911_{self.uid}_ip_id:{ip_id}")
        if ip_id:
            if db.mysql.get("""select 1 from t_user_tokens  tu left join t_proxy_ips  tp on tu.pi_id=tp.id 
                    where tp.ip_id = %s and tu.uid=%s and tu.deleted_on=0 and tu.expired=0
                     """, ip_id['id'], self.uid):
                print(f"911_{self.uid}_indb:{1}")
                return 0

        country_code = res_data.get("country")
        country_row = db.mysql.get("select 1 from t_geo_country where code = %s and id =%s", country_code,self.area_ids[0])
        print(f"911_{self.uid}_self.area_ids: {self.area_ids}")
        if not country_row:
            return 0
        state = res_data.get("region")
        if self.area_ids[1]:
            state_row = db.mysql.get("select 1 from t_geo_state where id=%s and name like %s", self.area_ids[1],
                                     f"%{state}%")
            if not state_row:
                return 0

        city = res_data.get("city")
        if self.area_ids[2]:
            city_row = db.mysql.get("select 1 from t_geo_city where id=%s and name like %s", self.area_ids[2],
                                    f"%{city}%")
            if not city_row:
                return 0
        return res_ip

    def callMachine(self, p_ip_id:int, data:dict):
        """
        呼叫机器生成代理接口
        :param p_ip_id
        :param data
        :return: 返回生成代理的big_int类型的ip 失败返回0    如果没有可用的机器 返回 None
        """
        if self.use_machine_id:   # 有指定特定机器
            machine_obj = get_machine_by_id(self.use_machine_id)
            if machine_obj['fault']:
                raise NoMachineError()
            port = data.get('port', db.redis.lpop(f"911_machine_{self.use_machine_id}"))
            if not port:
                raise MachinePortInsufficientError()
            m_id, m_ip, m_port = machine_obj['id'], str(ipaddress.ip_address(machine_obj['ip_addr'])), port
        else:  # 没有指定特定的机器
            m_id, m_ip, m_port = Choose911MachineFactory().target(self.ex_mids)
        if m_id == -1:
            return None
        self._expenditureOnePort(machine_id=m_id)
        data.update({'taskId': p_ip_id, 'port': m_port})
        res_ip = -1
        for i in range(6):   # 请求机器开代理重试两次
            res_ip = requestMachineNewProxy(machine_id=m_id, machine_ip=m_ip, data=data)
            if res_ip > 0 and self.check_area(res_ip, m_port):
                break
        else:
            return None
        # res_ip = requestMachineNewProxy(machine_id=m_id, machine_ip=m_ip, data=data)

        # 校验重复和地区是否一致的问题
        # if db.mysql.get("select 1 from t_users where email like '%%@adswave.com%%' and uid=%s", self.uid):


        if res_ip < 0:
            fault_num = db.redis.get(f"fault_machine_id_{m_id}")
            if fault_num and int(fault_num) >= 2:  # 允许两次请求失败才 标记为故障
                set_machine_fault(m_id, fault=1)
            else:   # 累计错误次数
                db.redis.setex(f"fault_machine_id_{m_id}", 60 * 60, (int(fault_num) + 1) if fault_num else 1)   # 错误次数+1维持1h
            self._recoverOnePort(m_id, m_port)
            self.ex_mids.append(m_id)   # 下次排除这个机器
            self.retry_num += 1      # 重试次数+1
            if self.retry_num >= self.RETRY_LIMIT:  # 超过制定最大尝试次数直接返回失败
                return None
            return self.callMachine(p_ip_id, data)
        elif res_ip == 0:
            self._recoverOnePort(m_id, m_port)
        elif res_ip > 0:
            db.redis.delete(f"fault_machine_id_{m_id}")
        return m_id, m_ip, m_port, res_ip

    def buy(self, p_ip_id=None, data=None) -> int:
        """

        :param p_ip_id:
        :return:   返回 t_proxy_ips.id, 是否成功
        """
        p_ip_id = p_ip_id or add_proxy_ips(country_id=self.area_ids[0], state_id=self.area_ids[1],
                                           city_id=self.area_ids[2], online=True, status=False,
                                           health=True)  # 如果没有指定代理表中那一条数据就新建一个数据
        data = data or self.requestParameters()
        res = self.callMachine(p_ip_id, data)
        print(f"911_{self.uid}_callMachineRes: {res}")
        if res is None:
            raise MachinePortInsufficientError()
        m_id, m_ip, m_port, res_ip = res
        if res_ip <= 0:
            return 0
        self._ip = ipaddress.ip_address(res_ip)
        change_proxy_ips_machine_and_port(p_ip_id, m_id, port=m_port, forward_port=m_port)
        insert_ip_id(proxy_ip_id=p_ip_id, ip_addr=res_ip)  # ip保存到 t_ips 和 t_proxy_ips
        same_proxy = get_same_ip(res_ip)
        if same_proxy:  # 如果出现重复的代理，这时候只用之前的一个，并把这个端口释放掉
            if request2Destroy(m_ip, p_ip_id):  # 请求关闭这个代理
                self._recoverOnePort(m_id, m_port)
            p_ip_id = same_proxy['id']
        else:
            if self.area_ids[0] == 0:
                country, state, city = get_ip_area(res_ip)
                change_proxy_ip_area(p_ip_id=p_ip_id, country_id=country[0], state_id=state[0], city_id=city[0])
            change_proxy_ips_status(p_ip_id=p_ip_id, status=True)
        return p_ip_id

    @abc.abstractmethod
    def requestParameters(self) -> dict:
        pass

    def choose(self) -> int:
        return self.buy(p_ip_id=self.pi_id, data=self.data)


class BuyProxyByIP(BuyProxy, ByIP):

    def buy(self, p_ip_id=None, data=None) -> int:
        if not self.fuzzy_query:
            same_ip = get_same_ip(int(self.ip_addr))
            if same_ip:
                return same_ip['id']
        return super().buy(p_ip_id=p_ip_id, data=data)
    
    @staticmethod
    def checkFuzzyQueryIpFormat(ip_addr: str) -> bool:
        """
        checkFuzzyQueryIpFormat 检查模糊查询IP格式是否正确

        :param str ip_addr:   字符串类型ip
        :return bool: 格式是否正确
        """
        ip_format_ok, ip_last_num_seat = True, -1   # 记录模糊查询IP格式是否正确    # 记录ip最后一个非*的倒序位置
        for idx, digit in enumerate(ip_addr.replace(".", "")[::-1]):   # 去掉点后倒序查询
            if len(digit) > 3:
                return False
            elif digit != '*' and ip_last_num_seat < 0:
                ip_last_num_seat = idx
            elif digit == "*" and ip_last_num_seat >= 0:
                ip_format_ok = False
                break
        return ip_format_ok
    
    @staticmethod
    def changeFuzzyQueryIp(ip_addr: str): # -> (str, str):
        """
        changeFuzzyQueryIp 把模糊查询IP中的*换成 开始结束IP

        :param str ip_addr: 字符串类型的IP
        :return (str, str): 开始IP, 结束ip
        """
        ip_addr_start_lit, ip_addr_end_lit = [], []
        for frag in ip_addr.split("."):
            if frag in ("*", "***", "**"):   # 字段是单独 * ** *** 替换为 0~255
                ip_addr_start_lit.append('0')
                ip_addr_end_lit.append('255')
            else:  # 字段是* 和数字结合的  把* 替换成 0~9
                ip_addr_start_lit.append(str(int(frag.replace('*', '0'))))
                ip_addr_end_lit.append(str(int(frag.replace('*', '9'))))
        return '.'.join(ip_addr_start_lit), '.'.join(ip_addr_end_lit)

    def requestParameters(self) -> dict:
        if not self.fuzzy_query:
            return {'freePort': 1, 'ipAddrStart': str(self.ip_addr), 'ipAddrEnd': str(self.ip_addr)}
        ip_addr = str(self.ip_addr)
        if not self.checkFuzzyQueryIpFormat(ip_addr):
            raise NoProxyError()
        ip_addr_start, ip_addr_end = self.changeFuzzyQueryIp(ip_addr)
        return {'freePort': 1, 'ipAddrStart': ip_addr_start, 'ipAddrEnd': ip_addr_end}


class BuyProxyByArea(BuyProxy, ByArea):

    def requestParameters(self) -> dict:
        data = {'freePort': 1}
        if self.country[0] == 0:
            return data
        data['geo'] = self.country[1]
        if self.state[0] == 0:
            return data
        data['state'] = self.state[2].replace(" ", "").lower()
        if self.city[0] == 0:
            return data
        data['city'] = self.city[1].replace(" ", "").lower()
        return data

    def buy(self, p_ip_id = None, data =None) -> int:
        self.area_ids = [self.country[0], self.state[0], self.city[0]]
        return super().buy(p_ip_id, data)

