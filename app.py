import logging
import os

import redis
import yaml
from celery import Celery
import db
import constants
from libs import feishu, torndb
from log import get_logger
from src import refresh_machines_use_condition
from utils.functions import MysqlConnPool
from utils.functions import DBS

constants.BASE_DIR = os.path.dirname(__file__)

# 加载配置文件
config = yaml.load(open(os.path.join(constants.BASE_DIR, constants.SETTINGS_FILE), 'r'), yaml.Loader)

# 连接mysql
constants.mysql_config = config['db']['mysql']
db.mysql = torndb.Connection(**constants.mysql_config)
# 连接1redis
pool = redis.ConnectionPool(**config['db']['redis'])
db.redis = redis.Redis(connection_pool=pool)

# 配置飞书
constants.feishu = feishu.FeiShu(id=config['feishu']['id'], secret=config['feishu']['secret'])

# 配置代理转发服务器地址
constants.PROXY_SERVERS_LIST.extend(config['server']['proxy'])
constants.CN_PROXY_SERVERS_LIST.extend(config['server']['cn_proxy'])

# 配置IPINFO
constants.IP_INFO_TOKEN = config['ipinfo']['token']

# 配置虚拟远程Windows参数
constants.WIN_VM = config['vm']

# 配置Azure服务器生成静态代理的参数
constants.AZURE_IP = config['azure_static_ip']

# iproyal
constants.IPROYAL_TOKEN = config['iproyal']['token']

# 刷新/获取机器的列表方法哦缓存
refresh_machines_use_condition()

app = Celery('proxy302', **config['celery'])  # 实例化celery对象

app.config_from_object("celery_config")

# 配置log

# 获取 logger 对象，并将其配置为使用 logging 模块中的配置
logger = get_logger(__name__)
logger.setLevel(logging.INFO)
logger.handlers = logging.getLogger().handlers
logger.propagate = False

"""
run worker: 
    celery  -A app worker -c 1 -l info -P prefork
"""
