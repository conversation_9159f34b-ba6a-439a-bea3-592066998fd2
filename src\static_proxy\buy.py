import ipaddress
from src.exceptions import *
from src.base import MakeProxyBase, ByProxyID
from models.proxy import *
from utils.functions import testProxy


class Buy(MakeProxyBase, ByProxyID):

    def __init__(self):
        self.setting = get_sell_setting("static_proxy")
        self.is_static = True

    def check_proxy(self, machine, proxy_obj) -> bool:
        _proxy = f"{'http' if machine['only_http'] else 'socks5'}://"
        if 'username' in proxy_obj and proxy_obj['username']:
            _proxy += f"{proxy_obj['username']}:{proxy_obj['password']}@"
        _proxy += f"{str(ipaddress.ip_address(machine['ip']))}:{proxy_obj['forward_port']}"
        return testProxy(_proxy)

    def choose(self):
        if not self.uid:
            raise UserError()
        proxy_obj = get_proxy_ip(self.proxy_id)
        if not proxy_obj:   # 代理不存在
            raise NoProxyError()
        machines = {machine['id']: {
            'ip': str(ipaddress.ip_address(machine['ip_addr'])),
            'supplier': machine['suppliers'], # if machine['suppliers'] != "aliyun" else "CN_Proxy",
            'only_http': machine['only_http']
        } for machine in get_all_machines()}
        # # 使用supplier判断是否为静态代理
        # if not proxy_obj or machines[proxy_obj['pm_id']]['supplier'] not in ("CN_Proxy"):
        #     raise NoProxyError()
        #####################################
        # 使用port是否为0判断是否为静态代理
        if proxy_obj['port'] != 0:
            raise NoProxyError()
        user_using_ip = tuple(set([proxy['id'] for proxy in get_user_using_proxy(self.uid) if not proxy['expired']]))
        if proxy_obj['id'] in user_using_ip:
            raise UserUsingThisProxyError()
        if machines[proxy_obj['pm_id']]['only_http'] and self.network in ('socket', 'socks', 'socks5') :   # 选择了socks5 但是代理不支持socks5
            raise ThisProxyNotSupplierSocksError()
        if not self.check_proxy(machines[proxy_obj['pm_id']], proxy_obj):
            raise ProxyNotOnlineError()
        return proxy_obj['id']

