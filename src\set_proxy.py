import abc

import constants
from models.proxy import *
from src.base import ResponseBase
from utils.functions import languageChecker


class SetToken(ResponseBase):
    def __init__(self, token_id:int, uid:int=None, lang:str='us'):
        self.uid, self.token_id = uid, token_id
        self.lang = languageChecker(lang)

    def do(self, **kwargs) -> dict:
        token_obj = get_user_token_by_id(self.token_id)
        if not token_obj:       # 找不到token
            return self.returnError(constants.ERR_NO_THIS_USER_TOKEN)
        if self.uid and token_obj['uid'] != self.uid:   # token的用户ID不对应
            return self.returnError(constants.ERR_NO_THIS_USER_TOKEN)
        if self.uid is None:
            self.uid = token_obj['uid']
        return self._do(token_obj, **kwargs)

    @abc.abstractmethod
    def _do(self, token_obj:dict, **kwargs) -> dict:
        pass


class SetTokenRemark(SetToken):
    """  User Token备注修改 """
    def _do(self, token_obj, **kwargs) -> dict:
        if token_obj['remark'] == kwargs['content']:    # 备注内容没有发生变化 返回成功
            return self.returnSucceed()
        if set_token_remark(self.uid, self.token_id, kwargs['content']):
            return self.returnSucceed()
        return self.returnError(constants.ERR_INTERNAL_ERROR)

