import datetime
import json

import db
from app import app
from src import *
from src.static_proxy.buy_azure_ip import AzureStaticProxy
from src.static_proxy.buy_private_ip import (
    BuyPrivateStaticIPDataCenterPrivateStaticIP,
    ByResidentialPrivateStaticIP,
    BuyPrivateStaticIP, BuyPrivateStaticIPDataCenterPrivateStaticIPSocks5,
)

import time
from log import get_logger
from src.static_proxy.renew import check_proxy_by_token_id

log = get_logger(__name__)


@app.task
def show_tokens(uid=None, after_ts=None, before_ts=None, status="") -> list:
    """
    展示用户拥有的静态代理
    :param uid:         :type: int  用户ID， default:None(内部使用 显示所有用户的)
    :param after_ts     :type: int  筛选时间(时间戳)之后的token数据
    :param before_ts    :type: int  筛选时间(时间戳)之前的token数据
    :param status       :type: str  根据代理状态过滤，default: "", 有效值有 'normal', 'expired', 'unhealthy'。非有效值显示全部
    :return:            :type: list 返回token的列表
    """
    return SearchStaticProxy(uid).tokens(after_ts, before_ts, status)


@app.task
def search_token(username: str, password: str, uid: int = None) -> dict:
    """
    通过token 的username和password查找对应静态代理的token数据
    :param username:    :type: str  token的用户名
    :param password:    :type: str  token的密码
    :param uid:         :type: int  用户ID， default:None(内部使用 显示所有用户的)
    :return:            :type: dict token的具体详细数据，如果有多条相同的token只返回最新的一条
    """
    return SearchStaticProxy(uid).byTokenUsername(username, password)


@app.task
def display(uid: int = None, limit: int = 1, offset: int = 10) -> dict:
    """
    展示可选的静态代理
    :param uid:     :type: int  用户ID， default:None, 此参数用于排除某个用户ID  None不排除
    :param limit:   :type: int  起始量，从1开始
    :param offset:  :type: int  偏移量 default: 10
    :return:        :type: dict 返回静态代理数据列表  ['proxies']: # 代理数据 #type: list,  ['total']: # 总数 #type: int, ['machines']: # 所有机器的数据 #type: list,
    """
    return SearchStaticProxy(uid).display(limit, offset)


@app.task
def search_by_ip(ip_addr, uid=None, network="http") -> dict:
    """
    通过IP地址（模糊）查找静态代理，并随机返回一个符合要求的
    :param ip_addr: :type: int or str   整形或字符串类型IP地址（可使用*进行模糊查询）
    :param uid:     :type: int          用户ID，此参数为用户ID时，不会返回用户已经在使用的代理，如果符合条件的代理都是被用户使用 会返回一个using为True的结果 default:None
    :param network: :type: str          网络协议类型,可选: None, http(https), socks(socket/socks5)，default:http
    :return:        :type: dict         返回符合条件的代理数据
    """
    return SearchStaticProxy(uid).byIP(ip_addr=ip_addr, network=network)


@app.task
def search_by_area(country=None, state=None, city=None, uid=None, network="http") -> dict:
    """
    通过地区查找静态代理，并随机返回一个符合要求的
    :param country: :type: str or int   国家，可以使用country数据库中的id或者国家的缩写  default: None(0):随机
    :param state:   :type: str or int   省/州，可以使用state数据库中的id或者省/州的缩写  default: None(0):随机
    :param city:    :type: str or int   城市，可以使用city数据库中的id或者城市的英文  default: None(0):随机
    :param uid:     :type: int          用户ID，此参数为用户ID时，不会返回用户已经在使用的代理，如果符合条件的代理都是被用户使用 会返回一个using为True的结果 default:None
    :param network: :type: str          网络协议类型,可选: None, http(https), socks(socket/socks5)，default:http
    :return:        :type: dict         返回符合条件的代理数据
    """
    return SearchStaticProxy(uid).byArea(country=country, state=state, city=city, network=network)


@app.task
def buy(uid, proxy_id, life_time, network="http") -> int:
    """
    购买静态代理
    :param uid:         :type: int  用户ID
    :param proxy_id:    :type: int  要购买的代理的ID
    :param life_time:   :type: int  购买的有效期限
    :param network:     :type: str  网络协议类型,可选: None, http(https), socks(socket/socks5)，default:http
    :return:            :type: int  token ID
    """
    return MakeStaticIPProxy(uid).buy(proxy_id=proxy_id, network=network, life_time=life_time)


@app.task
def rebuy(uid, token_id, life_time) -> int:
    """
    再次购买静态代理
    :param uid:         :type: int  用户ID
    :param token_id:    :type: int  要再次购买的代理对应的token ID
    :param life_time:   :type: int  购买的有效期限
    :return:            :type: int  token ID
    """
    return MakeStaticIPProxy(uid).rebuy(user_token_id=token_id, life_time=life_time)


@app.task
def renew(uid, token_id, life_time) -> int:
    """
    延长购买静态代理
    :param uid:         :type: int  用户ID
    :param token_id:    :type: int  要再次购买的代理对应的token ID
    :param life_time:   :type: int  购买的有效期限
    :return:            :type: int  token ID
    """
    return MakeStaticIPProxy(uid).renew(user_token_id=token_id, life_time=life_time)


@app.task
def buy_azure_ip(azure_location_id: int, payway_id: int, protocol="http", uid: int = None, lang="us") -> dict:
    """
    购买通过Azure服务器生成的静态代理
    :param azure_location_id:   :type: str  t_azure_locations.id
    :param payway_id:           :type: int  t_payways.id
    :param protocol:            :type: str  代理协议 'socks' or 'http'
    :param uid:                 :type: int  用户ID
    :param lang:                :type: str  语言。 'us':英文,'zh':中文  default:‘us’ 参数是不支持语言的时候为英文
    :return:
    """
    az_static_ip = AzureStaticProxy(uid, lang=lang)
    return az_static_ip.create(azure_location_id, payway_id, protocol)


@app.task
def renew_azure_ip(token_id: int, payway_id: int, uid: int = None, lang="us") -> dict:
    """
    延长通过Azure服务器生成的静态I代理设使用期限
    :param token_id:    :type: int  要再次购买的代理对应的token ID
    :param payway_id:   :type: int  t_payways.id
    :param uid:         :type: int  用户ID,为None的时候不扣用户余额， 内部用
    :param lang:        :type: str  语言。 'us':英文,'zh':中文  default:‘us’ 参数是不支持语言的时候为英文
    :return:
    """
    az_static_ip = AzureStaticProxy(uid, lang=lang)
    return az_static_ip.renew(token_id, payway_id)


@app.task
def buy_private_static_ip(
    payway_id: int, is_data_center: bool, country_id: int, uid: int = None, protocol="http", lang="us",is_auto_renew=False
) -> dict:
    """
    生成静态独享代理
    :param payway_id:       :type: int  t_payways.id
    :param is_data_center   :type bool  是不是数据中心代理
    :param country_id       :type int   国家id
    :param uid:             :type: int  用户ID
    :param protocol:        :type: str  代理协议 'socks' or 'http'
    :param lang:            :type: str  语言。 'us':英文,'zh':中文  default:‘us’ 参数是不支持语言的时候为英文
    :param is_auto_renew:            :type: bool 是否自动续费
    :return:
    """

    start_time = time.time()
    # 创建代理对象
    if is_data_center:
        if protocol == "http":
            class_obj = BuyPrivateStaticIPDataCenterPrivateStaticIP
        else:
            class_obj = BuyPrivateStaticIPDataCenterPrivateStaticIPSocks5
    else:
        class_obj = ByResidentialPrivateStaticIP

    private_static_ip = class_obj(payway_id)
    private_static_ip.uid = uid
    private_static_ip.lang = lang
    private_static_ip.network = protocol
    private_static_ip.is_auto_renew = is_auto_renew
    private_static_ip.setArea(country_id, 0, 0)

    db.mysql.execute("insert into t_user_logs (created_on,modified_on,uid,log_type_id,msg) values (%s,%s,%s,9,%s)",
                     int(time.time()), int(time.time()), uid, json.dumps({"payway_id":payway_id, "is_data_center":is_data_center, "country_id":country_id}))

    # 记录函数调用日志
    log.info(f"buy_private_static_ip called with payway_id={payway_id}, is_data_center={is_data_center}, country_id={country_id}, uid={uid}, protocol={protocol}, lang={lang}")

    # 执行函数
    result = private_static_ip.buy()
    # 判断如果是这个地方的就获取订单在看看要不要更新
    if isinstance(private_static_ip,ByResidentialPrivateStaticIP):
        check_proxy_by_token_id(result.get("data",{}).get("token_id"))
    end_time = time.time()
    elapsed_time = end_time - start_time

    # 记录函数执行时间
    log.info(f"buy_private_static_ip finished in {elapsed_time:.4f} seconds")
    return result


@app.task
def renew_private_static_ip(token_id: int, payway_id: int, uid: int = None, lang="us",**kwargs) -> dict:
    """
    续费静态独享代理
    :param token_id:    :type: int  要续费的代理对应的token ID
    :param payway_id:   :type: int  t_payways.id
    :param uid:         :type: int  用户ID,为None的时候不扣用户余额， 内部用
    :param lang:        :type: str  语言。 'us':英文,'zh':中文  default:‘us’ 参数是不支持语言的时候为英文
    :return:
    """
    private_static_ip = BuyPrivateStaticIP(payway_id)
    private_static_ip.uid = uid
    private_static_ip.lang = lang
    is_auto_renew = kwargs.get("is_auto_renew",'false')
    private_static_ip.is_auto_renew = True if is_auto_renew and is_auto_renew != "false" else False
    execute_user = kwargs.get("execute_user",uid)
    if not execute_user:
        execute_user = uid
    private_static_ip.execute_user = execute_user


    return private_static_ip.renew(token_id)


@app.task
def rebuy_private_static_ip(token_id: int, payway_id: int, uid: int = None, lang="us") -> dict:
    """
    续费静态独享代理
    :param token_id:    :type: int  要再次购买的代理对应的token ID
    :param payway_id:   :type: int  t_payways.id
    :param uid:         :type: int  用户ID,为None的时候不扣用户余额， 内部用
    :param lang:        :type: str  语言。 'us':英文,'zh':中文  default:‘us’ 参数是不支持语言的时候为英文
    :return:
    """
    private_static_ip = BuyPrivateStaticIP(payway_id)
    private_static_ip.uid = uid
    private_static_ip.lang = lang
    return private_static_ip.rebuy(token_id)
