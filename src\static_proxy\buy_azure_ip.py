import random

import _thread

import db
from libs import torndb
from libs.az import AZController
import constants
from models.vm import save_machine
from src.base import ResponseBase
from models.proxy import *
from models.ip_order import *
from utils.functions import language<PERSON>hecker, randomString


class AzureStaticProxy(ResponseBase):
    uid = None
    lang = 'us'

    def __init__(self,  uid:int=None, lang:str='us'):
        self.uid, self.lang = uid, languageChecker(lang)
        self.az = AZController(group=constants.AZURE_IP['group'],
                               image=constants.AZURE_IP['image'],
                               vm_name_prefix=constants.AZURE_IP['vm_name_prefix'],
                               size=constants.AZURE_IP['size'],
                               admin_username=constants.AZURE_IP['admin_username'],
                               admin_password=constants.AZURE_IP['admin_password'])

    @staticmethod
    def getBuyAzureStaticIPPayway(payway_id:int) -> Optional[dict]:
        payway = get_payway_by_id(payway_id)
        return None if not payway or payway['payway'] != 'buy_azure_ip' else payway

    def __pay(self, payway:dict) :
        return set_ip_order(
            uid=self.uid, type=payway['type'], currency_type="", currency=0, receive_currency=0, is_inner=False,
            payway=payway['payway'], pay_order="", value=payway['pay_value'], extra_value=payway['extra_value'],
            status=1, checksum=True, valid=True)

    def __refund(self, payway:dict, refund_payway:dict, user_token_id: int = 0):
        set_ip_order(
            uid=self.uid, type=refund_payway['type'], currency_type="", currency=0, receive_currency=0, is_inner=False,
            payway=refund_payway['payway'], pay_order="", value=payway['pay_value'],
            extra_value=payway['extra_value'], status=2, checksum=True, valid=True, ut_id=user_token_id)

    def renew(self, token_id: int, payway_id: int) -> dict:
        """
        续期
        :param token_id:
        :param payway_id:
        :return:
        """
        payway = self.getBuyAzureStaticIPPayway(payway_id)
        if not payway:
            return self.returnError(constants.ERR_NOT_PAYWAY)
        order_id = self.__pay(payway)
        if not order_id:
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH)
        if not change_user_token_life_time(token_id, life_time=payway['days']):  # 没有成功添加天数
            change_ip_order_status_by_order_id(order_id, status=2)  # 扣费账单设置为隐藏
            if payway:
                refund_payway = get_payway('refund_buy_azure_ip')
                self.__refund(payway, refund_payway, user_token_id=token_id)
            return self.returnError(constants.ERR_PROXY_TOKEN_EXPIRED)
        return self.returnSucceed()

    def create(self, azure_location_id: int, payway_id, protocol="http") -> dict:
        """
        创建
        :param azure_location_id:
        :param payway_id:
        :param protocol:
        :return:
        """
        try:
            setting = get_sell_setting_by_type("create_azure_ip_limit")
        except:
            setting = None
        limit = setting['how_many'] if setting and setting['on_off'] else 1
        if limit > 0:
            if int(db.redis.get(f"create_azure_ip_num_uid_{self.uid}") or 0) >= limit:
                return self.returnError(constants.ERR_AZURE_LIMIT)
        location = get_azure_location_by_id(azure_location_id)
        if not location or 'nsg' not in location:
            return self.returnError(constants.ERR_AREA_ERROR, lang=self.lang)
        proxy_port = constants.AZURE_IP['proxy_port']
        pi_id = add_proxy_ips(status=True, online=True, health=False, country_id=location['country_id'],
                              # state_id=location['state_id'], city_id=location['city_id'],
                              source='azure', forward_port=proxy_port, port=proxy_port,
                              username=constants.AZURE_IP['proxy_username'],
                              password=constants.AZURE_IP['proxy_password'])
        if not pi_id:
            return self.returnError(constants.ERR_INTERNAL_ERROR)
        payway = self.getBuyAzureStaticIPPayway(payway_id)
        if not payway:
            return self.returnError(constants.ERR_NOT_PAYWAY)
        order_id = self.__pay(payway)
        if not order_id:
            return self.returnError(constants.ERR_BALANCE_NOT_ENOUGH)
        token_username, token_password = randomString(8), randomString(16)
        url = random.choice(constants.PROXY_SERVERS_LIST)
        token_id = add_user_token(uid=self.uid, url=url, username=token_username, passwd=token_password, pi_id=pi_id,
                                  network=protocol, life_time=payway['days'], is_static=True)
        if not token_id:
            return self.returnError(constants.ERR_INTERNAL_ERROR)
        set_order_user_token_id(order_id, token_id)
        self.az.setNSG(location['nsg'])
        self.az.setVMCombo(location['location'], constants.AZURE_IP['size'])
        _thread.start_new_thread(self._create_vm, (pi_id, proxy_port, order_id, payway))
        return self.returnSucceed()

    def _create_vm(self, pi_id, port, order_id, payway, user_token_id=0):
        """
        多线程运行创建命名
        :param pi_id:
        :param port
        :param order_id:
        :param payway:
        :param user_token_id:
        :return:
        """
        db_session = torndb.Connection(**constants.mysql_config)
        vm_name = constants.AZURE_IP['vm_name_prefix'] + str(pi_id)
        log.info(f"正在创建机器: {vm_name}")
        db.redis.incr(f"create_azure_ip_num_uid_{self.uid}")
        db.redis.expire(f"create_azure_ip_num_uid_{self.uid}", 900)
        try:
            self.az.createVM(vm_name)
        except:
            traceback.print_exc()
        now_num = db.redis.get(f"create_azure_ip_num_uid_{self.uid}")
        if now_num and int(now_num) > 0:
            db.redis.set(f"create_azure_ip_num_uid_{self.uid}", int(now_num) - 1)
        log.info(f"已创建机器:{vm_name}")
        vm = None
        for _vm in list(self.az.getVMList().values())[::-1]:
            if vm_name == _vm['name']:
                vm = _vm
        print(vm)
        if not vm:
            log.error("创建失败, 现有机器中没有找到刚创建的机器")
            change_proxy_ips_status_online_health(pi_id, status=1, online=0, health=0)      # 设置状态为创建失败
            change_ip_order_status_by_order_id(order_id, status=2)             # 扣费账单设置为隐藏
            if payway:
                refund_payway = get_payway('refund_azure_ip')
                self.__refund(payway, refund_payway, user_token_id=user_token_id)
            return
        log.info(f"创建成功，{vm}")
        db.redis.delete(f"creating_win_vm_uid_{self.uid}")
        ps_id = get_supplier_by_name("azure_static_ip")['id']
        pm_id = save_machine(ip_addr=vm['public_ip'], ps_id=ps_id, port=port, vm_name=vm['name'],
                             public_ip=vm['public_ip'], db_session=db_session)
        insert_ip_id(pi_id, vm['public_ip'], db_session=db_session)
        change_proxy_ips_machine_and_port(pi_id, pm_id, port, port, db_session=db_session)
        change_proxy_ips_status_online_health(pi_id, status=1, online=1, health=1)
