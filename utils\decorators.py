import datetime
import decimal
import json
import time
from functools import wraps
import logging as log
import db



def timer(foo):
    """
    计算函数的运行用时
    """
    def wrapper(*args, **kwargs):
        start_ts = time.time()
        log.info(f"Function: {foo.__name__} start at {datetime.datetime.fromtimestamp(start_ts).strftime('%Y-%m-%d %H:%M:%S')}.")
        rst = foo(*args, **kwargs)
        end_ts = time.time()
        log.info(f"Function: {foo.__name__} end at {datetime.datetime.fromtimestamp(start_ts).strftime('%Y-%m-%d %H:%M:%S')}. Elapsed time {end_ts - start_ts}s")
        return rst
    return wrapper


def cache(timeout:int):
    """
    缓存装饰器
    :param timeout: 缓存到期时间
    :return:
    """
    def inner(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            key = ("func_" + func.__name__.upper()                                                          # 函数名
                   + (("_" + "_".join([str(arg) for arg in args])) if args else "")                         # 参数
                   + (("_" + "_".join(f"{str(k).upper()}_{str(v)}" for k, v in kwargs)) if kwargs.items() else "")) # 指定参数
            value = db.redis.get(key)
            if value:
                # print(f"use cache {key}")
                if isinstance(value, bytes):
                    value = value.decode("utf-8")
                return json.loads(value)
            res = func(*args, **kwargs)
            # print(f"no cache, set cache {key}")
            db.redis.setex(key, timeout, json.dumps(res, cls=DecimalEncoder))
            return res
        return wrapper
    return inner


class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, decimal.Decimal):
            return float(o)
        return super(DecimalEncoder, self).default(o)


